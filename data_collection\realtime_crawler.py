#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时股票监控爬虫
用于获取单只股票的实时信息
"""

import requests
import json
import time
import pymysql
from datetime import datetime
import pytz
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import DatabaseConfig, CrawlerConfig, log_error

def setup_realtime_logger():
    """设置实时爬虫专属日志记录器"""
    # 创建logs目录
    log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'logs')
    os.makedirs(log_dir, exist_ok=True)

    # 配置实时爬虫日志记录器
    realtime_logger = logging.getLogger('realtime_crawler')
    realtime_logger.setLevel(logging.INFO)

    # 清除已有的处理器
    realtime_logger.handlers.clear()

    # 文件处理器 - 记录到realtime_crawler.log
    realtime_log_file = os.path.join(log_dir, 'realtime_crawler.log')
    file_handler = logging.FileHandler(realtime_log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)

    # 日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)

    realtime_logger.addHandler(file_handler)

    return realtime_logger

def log_realtime_info(message):
    """记录实时爬虫信息日志"""
    logger = setup_realtime_logger()
    logger.info(message)

def log_realtime_warning(message):
    """记录实时爬虫警告日志"""
    logger = setup_realtime_logger()
    logger.warning(message)

class RealtimeCrawler:
    """实时股票监控爬虫"""
    
    def __init__(self):
        self.db_config = DatabaseConfig()
        self.crawler_config = CrawlerConfig()
        self.agricultural_stocks = self.crawler_config.AGRICULTURAL_STOCKS
        self.session = requests.Session()
        self.session.headers.update(self.crawler_config.DEFAULT_HEADERS)
        
    def get_stock_realtime_data(self, stock_code):
        """获取股票实时数据"""
        try:
            # 东方财富实时数据API
            url = "http://push2.eastmoney.com/api/qt/stock/get"
            params = {
                'ut': 'fa5fd1943c7b386f172d6893dbfba10b',
                'invt': '2',
                'fltt': '2',
                'fields': 'f43,f44,f45,f46,f47,f48,f58,f60,f116,f117,f162,f167,f168,f169,f170,f292',
                'secid': f'0.{stock_code}' if stock_code.startswith(('0', '3')) else f'1.{stock_code}',
                'cb': 'jQuery'
            }

            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()

            # 解析JSONP响应
            text = response.text
            if 'jQuery' in text:
                start = text.find('(') + 1
                end = text.rfind(')')
                json_str = text[start:end]
            else:
                json_str = text

            data = json.loads(json_str)

            if data.get('rc') == 0:  # rc=0表示请求成功
                stock_data = data.get('data', {})
                if stock_data:
                    return self.parse_stock_data(stock_code, stock_data)

            return None
            
        except Exception as e:
            log_error(f"获取股票 {stock_code} 实时数据失败: {e}", "realtime_crawler.get_stock_realtime_data", e)
            return None
    
    def parse_stock_data(self, stock_code, data):
        """解析股票数据"""
        try:
            # 使用北京时间
            beijing_tz = pytz.timezone('Asia/Shanghai')
            current_time = datetime.now(beijing_tz)

            # 解析各个字段
            parsed_data = {
                'stock_code': stock_code,
                'stock_name': data.get('f58', ''),  # 股票名称
                'current_price': data.get('f43', 0) / 100 if data.get('f43') else 0,  # 当前价格
                'change_amount': data.get('f169', 0) / 100 if data.get('f169') else 0,  # 涨跌额
                'change_percent': data.get('f170', 0) / 100 if data.get('f170') else 0,  # 涨跌幅
                'open_price': data.get('f46', 0) / 100 if data.get('f46') else 0,  # 开盘价
                'high_price': data.get('f44', 0) / 100 if data.get('f44') else 0,  # 最高价
                'low_price': data.get('f45', 0) / 100 if data.get('f45') else 0,  # 最低价
                'prev_close': data.get('f60', 0) / 100 if data.get('f60') else 0,  # 昨收价
                'volume': data.get('f47', 0),  # 成交量（手）
                'turnover': data.get('f48', 0) / 100 if data.get('f48') else 0,  # 成交额（元）
                'turnover_rate': data.get('f168', 0) / 100 if data.get('f168') else 0,  # 换手率
                'pe_ratio': data.get('f162', 0) / 100 if data.get('f162') else 0,  # 市盈率
                'pb_ratio': data.get('f167', 0) / 100 if data.get('f167') else 0,  # 市净率
                'market_cap': data.get('f116', 0) / 100 if data.get('f116') else 0,  # 总市值
                'circulating_market_cap': data.get('f117', 0) / 100 if data.get('f117') else 0,  # 流通市值
                'amplitude': data.get('f166', 0) / 100 if data.get('f166') else 0,  # 振幅
                'volume_ratio': data.get('f164', 0) / 100 if data.get('f164') else 0,  # 量比
                'update_time': current_time.strftime('%Y-%m-%d %H:%M:%S'),
                'trade_status': self.get_trade_status(data.get('f292', 0))  # 交易状态
            }

            return parsed_data

        except Exception as e:
            log_error(f"解析股票 {stock_code} 数据失败: {e}", "realtime_crawler.parse_stock_data", e)
            return None
    
    def get_trade_status(self, status_code):
        """获取交易状态"""
        status_map = {
            0: '停牌',
            1: '正常交易',
            2: '集合竞价',
            3: '临时停牌',
            4: '休市'
        }
        return status_map.get(status_code, '未知')
    
    def create_realtime_table(self, stock_code, start_time):
        """创建实时监控表"""
        try:
            connection = pymysql.connect(**self.db_config.MYSQL_CONFIG)
            cursor = connection.cursor()
            
            # 表名格式：股票代码_开始时间
            table_name = f"{stock_code}_{start_time.strftime('%Y%m%d_%H%M%S')}"
            
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS `{table_name}` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `stock_code` varchar(10) NOT NULL COMMENT '股票代码',
                `stock_name` varchar(50) NOT NULL COMMENT '股票名称',
                `current_price` decimal(10,3) DEFAULT NULL COMMENT '当前价格',
                `change_amount` decimal(10,3) DEFAULT NULL COMMENT '涨跌额',
                `change_percent` decimal(8,3) DEFAULT NULL COMMENT '涨跌幅(%)',
                `open_price` decimal(10,3) DEFAULT NULL COMMENT '开盘价',
                `high_price` decimal(10,3) DEFAULT NULL COMMENT '最高价',
                `low_price` decimal(10,3) DEFAULT NULL COMMENT '最低价',
                `prev_close` decimal(10,3) DEFAULT NULL COMMENT '昨收价',
                `volume` bigint(20) DEFAULT NULL COMMENT '成交量(手)',
                `turnover` decimal(15,2) DEFAULT NULL COMMENT '成交额(元)',
                `turnover_rate` decimal(8,3) DEFAULT NULL COMMENT '换手率(%)',
                `pe_ratio` decimal(10,3) DEFAULT NULL COMMENT '市盈率',
                `pb_ratio` decimal(10,3) DEFAULT NULL COMMENT '市净率',
                `market_cap` decimal(15,2) DEFAULT NULL COMMENT '总市值(元)',
                `circulating_market_cap` decimal(15,2) DEFAULT NULL COMMENT '流通市值(元)',
                `amplitude` decimal(8,3) DEFAULT NULL COMMENT '振幅(%)',
                `volume_ratio` decimal(10,3) DEFAULT NULL COMMENT '量比',
                `trade_status` varchar(20) DEFAULT NULL COMMENT '交易状态',
                `update_time` datetime NOT NULL COMMENT '更新时间',
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                PRIMARY KEY (`id`),
                KEY `idx_update_time` (`update_time`),
                KEY `idx_stock_code` (`stock_code`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时股票监控数据表';
            """
            
            cursor.execute(create_table_sql)
            connection.commit()
            
            log_realtime_info(f"创建实时监控表成功: {table_name}")
            print(f"[SUCCESS] Created realtime monitoring table successfully: {table_name}")
            return table_name
            
        except Exception as e:
            log_error(f"创建实时监控表失败: {e}", "realtime_crawler.create_realtime_table", e)
            return None
        finally:
            if 'connection' in locals():
                connection.close()
    
    def save_realtime_data(self, table_name, data):
        """保存实时数据到数据库"""
        try:
            connection = pymysql.connect(**self.db_config.MYSQL_CONFIG)
            cursor = connection.cursor()
            
            insert_sql = f"""
            INSERT INTO `{table_name}` (
                stock_code, stock_name, current_price, change_amount, change_percent,
                open_price, high_price, low_price, prev_close, volume, turnover,
                turnover_rate, pe_ratio, pb_ratio, market_cap, circulating_market_cap,
                amplitude, volume_ratio, trade_status, update_time
            ) VALUES (
                %(stock_code)s, %(stock_name)s, %(current_price)s, %(change_amount)s, %(change_percent)s,
                %(open_price)s, %(high_price)s, %(low_price)s, %(prev_close)s, %(volume)s, %(turnover)s,
                %(turnover_rate)s, %(pe_ratio)s, %(pb_ratio)s, %(market_cap)s, %(circulating_market_cap)s,
                %(amplitude)s, %(volume_ratio)s, %(trade_status)s, %(update_time)s
            )
            """
            
            cursor.execute(insert_sql, data)
            connection.commit()
            
            return True
            
        except Exception as e:
            log_error(f"保存实时数据失败: {e}", "realtime_crawler.save_realtime_data", e)
            return False
        finally:
            if 'connection' in locals():
                connection.close()
    
    def monitor_stock(self, stock_code, duration_minutes=30, interval_seconds=30):
        """监控单只股票"""
        try:
            # 验证股票代码
            if stock_code not in self.agricultural_stocks:
                print(f"[ERROR] Stock code {stock_code} not in monitoring list")
                return False
            
            stock_info = self.agricultural_stocks[stock_code]
            log_realtime_info(f"开始监控股票: {stock_info['name']} ({stock_code})")
            print(f"[STARTING] Starting to monitor stock: {stock_info['name']} ({stock_code})")

            # 创建监控表
            beijing_tz = pytz.timezone('Asia/Shanghai')
            start_time = datetime.now(beijing_tz)
            table_name = self.create_realtime_table(stock_code, start_time)
            if not table_name:
                log_error("创建监控表失败", "realtime_crawler.monitor_stock")
                return False
            
            # 开始监控
            end_time = start_time.timestamp() + (duration_minutes * 60)
            count = 0
            
            while time.time() < end_time:
                # 获取实时数据
                data = self.get_stock_realtime_data(stock_code)
                
                if data:
                    # 保存数据
                    if self.save_realtime_data(table_name, data):
                        count += 1
                        log_realtime_info(f"[{count}] {data['stock_name']} - 价格: ¥{data['current_price']:.2f} "
                                        f"涨跌: {data['change_percent']:+.2f}% - {data['update_time']}")
                        print(f"[DATA] [{count}] {data['stock_name']} - Price: ¥{data['current_price']:.2f} "
                              f"Change: {data['change_percent']:+.2f}% - {data['update_time']}")
                    else:
                        log_error("保存实时数据失败", "realtime_crawler.monitor_stock")
                        print(f"[ERROR] Failed to save data")
                else:
                    log_realtime_warning("获取实时数据失败")
                    print(f"[ERROR] Failed to fetch data")
                
                # 等待下次采集
                time.sleep(interval_seconds)
            
            log_realtime_info(f"监控完成，共采集 {count} 条数据，表名: {table_name}")
            print(f"[SUCCESS] Monitoring completed, collected {count} data records, table: {table_name}")
            return True
            
        except KeyboardInterrupt:
            print(f"\n[INTERRUPTED] Monitoring interrupted by user")
            return True
        except Exception as e:
            log_error(f"监控股票失败: {e}", "realtime_crawler.monitor_stock", e)
            return False

def main():
    """主函数 - 用于测试"""
    crawler = RealtimeCrawler()

    # 从46只农业股票中选择一只进行测试
    agricultural_stocks = list(crawler.agricultural_stocks.keys())
    test_stock = agricultural_stocks[0]  # 选择第一只农业股票
    stock_info = crawler.agricultural_stocks[test_stock]

    print(f"[TEST] Testing realtime data fetch for agricultural stock {stock_info['name']} ({test_stock})...")

    data = crawler.get_stock_realtime_data(test_stock)
    if data:
        print("[SUCCESS] Data fetch successful:")
        for key, value in data.items():
            print(f"  {key}: {value}")
    else:
        print("[ERROR] Data fetch failed")

if __name__ == '__main__':
    main()
