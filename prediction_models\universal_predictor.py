#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用LSTM股票预测模型
使用所有农业股票的数据训练两个通用模型：
1. 3天预测模型（基于天级数据）
2. 1小时预测模型（基于分钟级数据）
"""

import os
import sys
import numpy as np
import pandas as pd
import pickle
import logging
from datetime import datetime, timedelta
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
import tensorflow as tf
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint
import pymysql

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import DatabaseConfig, PredictionConfig, log_error

class UniversalStockPredictor:
    """通用股票预测模型"""
    
    def __init__(self):
        self.db_config = DatabaseConfig()
        self.prediction_config = PredictionConfig()
        
        # 模型配置
        self.model_config = self.prediction_config.MODEL_CONFIG
        self.daily_config = self.prediction_config.DAILY_PREDICTION
        self.hourly_config = self.prediction_config.HOURLY_PREDICTION
        
        # 农业股票列表
        try:
            from config import AGRICULTURAL_STOCKS
            self.agricultural_stocks = AGRICULTURAL_STOCKS
        except ImportError:
            # 如果导入失败，使用默认配置
            self.agricultural_stocks = {
                '000876': {'name': '新希望', 'category': '饲料养殖', 'industry': '养殖业'},
                '300498': {'name': '温氏股份', 'category': '生猪养殖', 'industry': '养殖业'},
                '002714': {'name': '牧原股份', 'category': '生猪养殖', 'industry': '养殖业'}
            }
        
        # 模型保存路径
        self.models_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'models', 'universal')
        os.makedirs(self.models_dir, exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'logs', 'universal_training')
        os.makedirs(log_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = os.path.join(log_dir, f'universal_training_{timestamp}.log')
        
        self.logger = logging.getLogger('universal_training')
        self.logger.setLevel(logging.INFO)
        
        # 清除现有的处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # 文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def get_all_daily_data(self):
        """获取所有农业股票的天级数据"""
        try:
            self.logger.info("开始收集所有农业股票的天级数据...")
            
            all_data = []
            connection = pymysql.connect(**self.db_config.MYSQL_CONFIG)
            
            for stock_code in self.agricultural_stocks.keys():
                try:
                    table_name = f"{stock_code}_daily"
                    
                    # 检查表是否存在
                    cursor = connection.cursor()
                    cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                    if not cursor.fetchone():
                        self.logger.warning(f"表 {table_name} 不存在，跳过")
                        continue
                    
                    # 获取数据
                    query = f"""
                        SELECT trade_date, close_price, volume, change_percent
                        FROM {table_name}
                        WHERE close_price IS NOT NULL 
                        AND volume IS NOT NULL 
                        AND change_percent IS NOT NULL
                        ORDER BY trade_date ASC
                    """
                    
                    df = pd.read_sql(query, connection)
                    if len(df) < self.daily_config['sequence_length']:
                        self.logger.warning(f"股票 {stock_code} 数据不足: {len(df)} < {self.daily_config['sequence_length']}")
                        continue
                    
                    # 添加股票标识
                    df['stock_code'] = stock_code
                    all_data.append(df)
                    self.logger.info(f"收集股票 {stock_code} 数据: {len(df)} 条记录")
                    
                except Exception as e:
                    self.logger.error(f"获取股票 {stock_code} 数据失败: {e}")
                    continue
            
            connection.close()
            
            if not all_data:
                self.logger.error("没有收集到任何有效数据")
                return None
            
            # 合并所有数据
            combined_data = pd.concat(all_data, ignore_index=True)
            self.logger.info(f"总共收集到 {len(combined_data)} 条天级数据记录，来自 {len(all_data)} 只股票")
            
            return combined_data
            
        except Exception as e:
            self.logger.error(f"获取天级数据失败: {e}")
            log_error(f"获取天级数据失败: {e}", "universal_predictor.get_all_daily_data", e)
            return None
    
    def get_all_minute_data(self):
        """获取所有农业股票的分钟级数据"""
        try:
            self.logger.info("开始收集所有农业股票的分钟级数据...")
            
            all_data = []
            connection = pymysql.connect(**self.db_config.MYSQL_CONFIG)
            
            for stock_code in self.agricultural_stocks.keys():
                try:
                    table_name = f"{stock_code}_minute"
                    
                    # 检查表是否存在
                    cursor = connection.cursor()
                    cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                    if not cursor.fetchone():
                        self.logger.warning(f"表 {table_name} 不存在，跳过")
                        continue
                    
                    # 获取最近的数据（避免数据量过大）
                    query = f"""
                        SELECT trade_datetime, close_price, volume, change_percent
                        FROM {table_name}
                        WHERE close_price IS NOT NULL 
                        AND volume IS NOT NULL 
                        AND change_percent IS NOT NULL
                        ORDER BY trade_datetime DESC
                        LIMIT 10000
                    """
                    
                    df = pd.read_sql(query, connection)
                    if len(df) < self.hourly_config['sequence_length']:
                        self.logger.warning(f"股票 {stock_code} 分钟级数据不足: {len(df)} < {self.hourly_config['sequence_length']}")
                        continue
                    
                    # 按时间正序排列
                    df = df.sort_values('trade_datetime').reset_index(drop=True)
                    
                    # 添加股票标识
                    df['stock_code'] = stock_code
                    all_data.append(df)
                    self.logger.info(f"收集股票 {stock_code} 分钟级数据: {len(df)} 条记录")
                    
                except Exception as e:
                    self.logger.error(f"获取股票 {stock_code} 分钟级数据失败: {e}")
                    continue
            
            connection.close()
            
            if not all_data:
                self.logger.error("没有收集到任何有效分钟级数据")
                return None
            
            # 合并所有数据
            combined_data = pd.concat(all_data, ignore_index=True)
            self.logger.info(f"总共收集到 {len(combined_data)} 条分钟级数据记录，来自 {len(all_data)} 只股票")
            
            return combined_data
            
        except Exception as e:
            self.logger.error(f"获取分钟级数据失败: {e}")
            log_error(f"获取分钟级数据失败: {e}", "universal_predictor.get_all_minute_data", e)
            return None
    
    def prepare_sequences(self, data, sequence_length, prediction_steps, features):
        """准备序列数据"""
        try:
            # 按股票分组处理
            X, y = [], []
            
            for stock_code in data['stock_code'].unique():
                stock_data = data[data['stock_code'] == stock_code].copy()
                
                if len(stock_data) < sequence_length + prediction_steps:
                    continue
                
                # 提取特征
                feature_data = stock_data[features].values
                
                # 创建序列
                for i in range(len(feature_data) - sequence_length - prediction_steps + 1):
                    # 输入序列
                    X.append(feature_data[i:(i + sequence_length)])
                    # 输出序列
                    y.append(feature_data[(i + sequence_length):(i + sequence_length + prediction_steps)])
            
            return np.array(X), np.array(y)
            
        except Exception as e:
            self.logger.error(f"准备序列数据失败: {e}")
            return None, None

    def build_lstm_model(self, input_shape, output_shape):
        """构建LSTM模型"""
        model = Sequential()

        # 第一层LSTM
        model.add(LSTM(units=self.model_config['lstm_units'],
                      return_sequences=True,
                      input_shape=input_shape))
        model.add(Dropout(self.model_config['dropout_rate']))

        # 第二层LSTM
        model.add(LSTM(units=self.model_config['lstm_units'],
                      return_sequences=True))
        model.add(Dropout(self.model_config['dropout_rate']))

        # 第三层LSTM
        model.add(LSTM(units=self.model_config['lstm_units'],
                      return_sequences=False))
        model.add(Dropout(self.model_config['dropout_rate']))

        # 输出层
        model.add(Dense(units=output_shape))

        # 编译模型
        model.compile(
            optimizer=Adam(learning_rate=self.model_config['learning_rate']),
            loss='mse',
            metrics=['mae']
        )

        return model

    def train_daily_model(self):
        """训练3天预测通用模型"""
        try:
            self.logger.info("=" * 60)
            self.logger.info("开始训练3天预测通用模型")
            self.logger.info("=" * 60)

            training_start_time = datetime.now()

            # 获取所有天级数据
            self.logger.info("步骤1: 获取所有农业股票的天级数据")
            data = self.get_all_daily_data()
            if data is None:
                self.logger.error("获取天级数据失败")
                return False

            # 准备特征
            features = self.daily_config['features']
            sequence_length = self.daily_config['sequence_length']
            prediction_days = self.daily_config['prediction_days']

            self.logger.info(f"特征列: {features}")
            self.logger.info(f"序列长度: {sequence_length}")
            self.logger.info(f"预测天数: {prediction_days}")

            # 数据标准化
            self.logger.info("步骤2: 数据标准化")
            scaler = MinMaxScaler()
            data[features] = scaler.fit_transform(data[features])

            # 准备序列数据
            self.logger.info("步骤3: 准备序列数据")
            X, y = self.prepare_sequences(data, sequence_length, prediction_days, features)

            if X is None or len(X) == 0:
                self.logger.error("序列数据准备失败")
                return False

            # 重塑y的形状以适应模型输出
            y = y.reshape(y.shape[0], -1)  # (samples, prediction_days * features)

            self.logger.info(f"输入数据形状: X={X.shape}, y={y.shape}")

            # 分割训练和测试数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, shuffle=True
            )

            self.logger.info(f"训练数据: X_train={X_train.shape}, y_train={y_train.shape}")
            self.logger.info(f"测试数据: X_test={X_test.shape}, y_test={y_test.shape}")

            # 构建模型
            self.logger.info("步骤4: 构建LSTM模型")
            input_shape = (X_train.shape[1], X_train.shape[2])
            output_shape = y_train.shape[1]
            model = self.build_lstm_model(input_shape, output_shape)

            self.logger.info(f"模型输入形状: {input_shape}")
            self.logger.info(f"模型输出形状: {output_shape}")

            # 设置回调
            callbacks = [
                EarlyStopping(patience=10, restore_best_weights=True),
                ModelCheckpoint(
                    os.path.join(self.models_dir, 'daily_model_best.h5'),
                    save_best_only=True,
                    monitor='val_loss'
                )
            ]

            # 训练模型
            self.logger.info("步骤5: 开始模型训练")
            history = model.fit(
                X_train, y_train,
                epochs=self.model_config['epochs'],
                batch_size=self.model_config['batch_size'],
                validation_data=(X_test, y_test),
                callbacks=callbacks,
                verbose=1
            )

            # 保存模型和标准化器
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            model_path = os.path.join(self.models_dir, f'daily_model_{timestamp}.h5')
            scaler_path = os.path.join(self.models_dir, f'daily_scaler_{timestamp}.pkl')

            model.save(model_path)
            with open(scaler_path, 'wb') as f:
                pickle.dump(scaler, f)

            # 保存最新模型（覆盖）
            latest_model_path = os.path.join(self.models_dir, 'daily_model_latest.h5')
            latest_scaler_path = os.path.join(self.models_dir, 'daily_scaler_latest.pkl')

            model.save(latest_model_path)
            with open(latest_scaler_path, 'wb') as f:
                pickle.dump(scaler, f)

            # 记录训练结果
            training_time = datetime.now() - training_start_time
            final_loss = history.history['loss'][-1]
            final_val_loss = history.history['val_loss'][-1]

            self.logger.info("步骤6: 训练完成")
            self.logger.info(f"训练时间: {training_time}")
            self.logger.info(f"最终训练损失: {final_loss:.6f}")
            self.logger.info(f"最终验证损失: {final_val_loss:.6f}")
            self.logger.info(f"模型已保存: {model_path}")
            self.logger.info(f"标准化器已保存: {scaler_path}")

            return True

        except Exception as e:
            self.logger.error(f"训练3天预测模型失败: {e}")
            log_error(f"训练3天预测模型失败: {e}", "universal_predictor.train_daily_model", e)
            return False

    def train_hourly_model(self):
        """训练1小时预测通用模型"""
        try:
            self.logger.info("=" * 60)
            self.logger.info("开始训练1小时预测通用模型")
            self.logger.info("=" * 60)

            training_start_time = datetime.now()

            # 获取所有分钟级数据
            self.logger.info("步骤1: 获取所有农业股票的分钟级数据")
            data = self.get_all_minute_data()
            if data is None:
                self.logger.error("获取分钟级数据失败")
                return False

            # 准备特征
            features = self.hourly_config['features']
            sequence_length = self.hourly_config['sequence_length']
            prediction_minutes = 12  # 预测12个5分钟间隔（1小时）

            self.logger.info(f"特征列: {features}")
            self.logger.info(f"序列长度: {sequence_length}")
            self.logger.info(f"预测间隔数: {prediction_minutes}")

            # 数据标准化
            self.logger.info("步骤2: 数据标准化")
            scaler = MinMaxScaler()
            data[features] = scaler.fit_transform(data[features])

            # 准备序列数据
            self.logger.info("步骤3: 准备序列数据")
            X, y = self.prepare_sequences(data, sequence_length, prediction_minutes, features)

            if X is None or len(X) == 0:
                self.logger.error("序列数据准备失败")
                return False

            # 重塑y的形状以适应模型输出
            y = y.reshape(y.shape[0], -1)  # (samples, prediction_minutes * features)

            self.logger.info(f"输入数据形状: X={X.shape}, y={y.shape}")

            # 分割训练和测试数据
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, shuffle=True
            )

            self.logger.info(f"训练数据: X_train={X_train.shape}, y_train={y_train.shape}")
            self.logger.info(f"测试数据: X_test={X_test.shape}, y_test={y_test.shape}")

            # 构建模型
            self.logger.info("步骤4: 构建LSTM模型")
            input_shape = (X_train.shape[1], X_train.shape[2])
            output_shape = y_train.shape[1]
            model = self.build_lstm_model(input_shape, output_shape)

            self.logger.info(f"模型输入形状: {input_shape}")
            self.logger.info(f"模型输出形状: {output_shape}")

            # 设置回调
            callbacks = [
                EarlyStopping(patience=10, restore_best_weights=True),
                ModelCheckpoint(
                    os.path.join(self.models_dir, 'hourly_model_best.h5'),
                    save_best_only=True,
                    monitor='val_loss'
                )
            ]

            # 训练模型
            self.logger.info("步骤5: 开始模型训练")
            history = model.fit(
                X_train, y_train,
                epochs=self.model_config['epochs'],
                batch_size=self.model_config['batch_size'],
                validation_data=(X_test, y_test),
                callbacks=callbacks,
                verbose=1
            )

            # 保存模型和标准化器
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            model_path = os.path.join(self.models_dir, f'hourly_model_{timestamp}.h5')
            scaler_path = os.path.join(self.models_dir, f'hourly_scaler_{timestamp}.pkl')

            model.save(model_path)
            with open(scaler_path, 'wb') as f:
                pickle.dump(scaler, f)

            # 保存最新模型（覆盖）
            latest_model_path = os.path.join(self.models_dir, 'hourly_model_latest.h5')
            latest_scaler_path = os.path.join(self.models_dir, 'hourly_scaler_latest.pkl')

            model.save(latest_model_path)
            with open(latest_scaler_path, 'wb') as f:
                pickle.dump(scaler, f)

            # 记录训练结果
            training_time = datetime.now() - training_start_time
            final_loss = history.history['loss'][-1]
            final_val_loss = history.history['val_loss'][-1]

            self.logger.info("步骤6: 训练完成")
            self.logger.info(f"训练时间: {training_time}")
            self.logger.info(f"最终训练损失: {final_loss:.6f}")
            self.logger.info(f"最终验证损失: {final_val_loss:.6f}")
            self.logger.info(f"模型已保存: {model_path}")
            self.logger.info(f"标准化器已保存: {scaler_path}")

            return True

        except Exception as e:
            self.logger.error(f"训练1小时预测模型失败: {e}")
            log_error(f"训练1小时预测模型失败: {e}", "universal_predictor.train_hourly_model", e)
            return False

    def predict_daily(self, stock_code):
        """使用通用模型预测3天走势"""
        try:
            # 加载最新的模型和标准化器
            model_path = os.path.join(self.models_dir, 'daily_model_latest.h5')
            scaler_path = os.path.join(self.models_dir, 'daily_scaler_latest.pkl')

            if not os.path.exists(model_path) or not os.path.exists(scaler_path):
                self.logger.error(f"通用3天预测模型不存在: {model_path} 或 {scaler_path}")
                return None

            # 加载模型和标准化器（修复Keras兼容性问题）
            try:
                # 尝试使用自定义对象加载模型
                model = load_model(model_path, custom_objects={'mse': tf.keras.metrics.MeanSquaredError()})
            except Exception as e:
                self.logger.warning(f"使用自定义对象加载模型失败，尝试重新编译: {e}")
                # 如果失败，加载模型并重新编译
                model = load_model(model_path, compile=False)
                model.compile(
                    optimizer='adam',
                    loss='mse',
                    metrics=['mse']
                )

            with open(scaler_path, 'rb') as f:
                scaler = pickle.load(f)

            # 获取股票最近的数据
            connection = pymysql.connect(**self.db_config.MYSQL_CONFIG)
            table_name = f"{stock_code}_daily"

            query = f"""
                SELECT trade_date, close_price, volume, change_percent
                FROM {table_name}
                WHERE close_price IS NOT NULL
                AND volume IS NOT NULL
                AND change_percent IS NOT NULL
                ORDER BY trade_date DESC
                LIMIT {self.daily_config['sequence_length']}
            """

            df = pd.read_sql(query, connection)
            connection.close()

            if len(df) < self.daily_config['sequence_length']:
                self.logger.error(f"股票 {stock_code} 数据不足: {len(df)} < {self.daily_config['sequence_length']}")
                return None

            # 按时间正序排列
            df = df.sort_values('trade_date').reset_index(drop=True)

            # 标准化数据
            features = self.daily_config['features']
            scaled_data = scaler.transform(df[features])

            # 准备输入数据
            X = scaled_data.reshape(1, self.daily_config['sequence_length'], len(features))

            # 进行预测
            prediction = model.predict(X)

            # 反标准化预测结果
            prediction_reshaped = prediction.reshape(self.daily_config['prediction_days'], len(features))
            prediction_original = scaler.inverse_transform(prediction_reshaped)

            # 生成未来日期
            last_date = df['trade_date'].iloc[-1]
            future_dates = []
            for i in range(1, self.daily_config['prediction_days'] + 1):
                future_date = last_date + timedelta(days=i)
                future_dates.append(future_date)

            # 构建结果DataFrame
            result_df = pd.DataFrame({
                'date': future_dates,
                'predicted_close_price': prediction_original[:, 0],
                'predicted_volume': prediction_original[:, 1],
                'predicted_change_percent': prediction_original[:, 2]
            })

            return result_df

        except Exception as e:
            self.logger.error(f"3天预测失败: {e}")
            log_error(f"3天预测失败: {e}", "universal_predictor.predict_daily", e)
            return None

    def predict_hourly(self, stock_code, steps=12):
        """使用通用模型预测1小时走势"""
        try:
            # 加载最新的模型和标准化器
            model_path = os.path.join(self.models_dir, 'hourly_model_latest.h5')
            scaler_path = os.path.join(self.models_dir, 'hourly_scaler_latest.pkl')

            if not os.path.exists(model_path) or not os.path.exists(scaler_path):
                self.logger.error(f"通用1小时预测模型不存在: {model_path} 或 {scaler_path}")
                return None

            # 加载模型和标准化器（修复Keras兼容性问题）
            try:
                # 尝试使用自定义对象加载模型
                model = load_model(model_path, custom_objects={'mse': tf.keras.metrics.MeanSquaredError()})
            except Exception as e:
                self.logger.warning(f"使用自定义对象加载模型失败，尝试重新编译: {e}")
                # 如果失败，加载模型并重新编译
                model = load_model(model_path, compile=False)
                model.compile(
                    optimizer='adam',
                    loss='mse',
                    metrics=['mse']
                )

            with open(scaler_path, 'rb') as f:
                scaler = pickle.load(f)

            # 获取股票最近的分钟级数据
            connection = pymysql.connect(**self.db_config.MYSQL_CONFIG)
            table_name = f"{stock_code}_minute"

            query = f"""
                SELECT trade_datetime, close_price, volume, change_percent
                FROM {table_name}
                WHERE close_price IS NOT NULL
                AND volume IS NOT NULL
                AND change_percent IS NOT NULL
                ORDER BY trade_datetime DESC
                LIMIT {self.hourly_config['sequence_length']}
            """

            df = pd.read_sql(query, connection)
            connection.close()

            if len(df) < self.hourly_config['sequence_length']:
                self.logger.error(f"股票 {stock_code} 分钟级数据不足: {len(df)} < {self.hourly_config['sequence_length']}")
                return None

            # 按时间正序排列
            df = df.sort_values('trade_datetime').reset_index(drop=True)

            # 标准化数据
            features = self.hourly_config['features']
            scaled_data = scaler.transform(df[features])

            # 准备输入数据
            X = scaled_data.reshape(1, self.hourly_config['sequence_length'], len(features))

            # 进行预测
            prediction = model.predict(X)

            # 反标准化预测结果
            prediction_reshaped = prediction.reshape(steps, len(features))
            prediction_original = scaler.inverse_transform(prediction_reshaped)

            # 生成未来时间
            last_datetime = df['trade_datetime'].iloc[-1]
            future_times = []
            for i in range(1, steps + 1):
                future_time = last_datetime + timedelta(minutes=i*5)
                future_times.append(future_time)

            # 构建结果DataFrame
            result_df = pd.DataFrame({
                'datetime': future_times,
                'predicted_close_price': prediction_original[:, 0],
                'predicted_volume': prediction_original[:, 1],
                'predicted_change_percent': prediction_original[:, 2]
            })

            return result_df

        except Exception as e:
            self.logger.error(f"1小时预测失败: {e}")
            log_error(f"1小时预测失败: {e}", "universal_predictor.predict_hourly", e)
            return None

    def train_all_models(self):
        """训练所有通用模型"""
        self.logger.info("开始训练所有通用模型")

        # 训练3天预测模型
        daily_success = self.train_daily_model()

        # 训练1小时预测模型
        hourly_success = self.train_hourly_model()

        if daily_success and hourly_success:
            self.logger.info("所有通用模型训练完成")
            return True
        else:
            self.logger.error("部分模型训练失败")
            return False

def main():
    """主函数"""
    print("农业股票通用LSTM预测模型训练系统")
    print("=" * 60)

    predictor = UniversalStockPredictor()

    choice = input("选择操作：1-训练所有模型 2-训练3天预测模型 3-训练1小时预测模型 4-测试预测 (默认1): ").strip()

    if choice == "2":
        predictor.train_daily_model()
    elif choice == "3":
        predictor.train_hourly_model()
    elif choice == "4":
        stock_code = input("请输入股票代码: ").strip()
        if stock_code in predictor.agricultural_stocks:
            print("\n测试3天预测:")
            daily_result = predictor.predict_daily(stock_code)
            if daily_result is not None:
                print(daily_result.to_string(index=False))

            print("\n测试1小时预测:")
            hourly_result = predictor.predict_hourly(stock_code)
            if hourly_result is not None:
                print(hourly_result.head(10).to_string(index=False))
        else:
            print("股票代码不在农业股票列表中")
    else:
        # 默认训练所有模型
        predictor.train_all_models()

if __name__ == "__main__":
    main()
