/**
 * 实时监控界面JavaScript
 */

class RealtimeMonitor {
    constructor() {
        this.charts = {};
        this.stocksData = null;
        this.currentStock = null;
        this.monitoringInterval = null;
        this.currentTableName = null;
        this.dataBuffer = {
            times: [],
            prices: [],
            changes: [],
            volumes: [],
            turnovers: [],
            turnoverRates: [],
            peRatios: [],
            pbRatios: []
        };
        this.maxDataPoints = 24; // 2分钟 / 5秒 = 24个数据点
        this.init();
    }

    /**
     * 初始化
     */
    init() {
        this.bindEvents();
        this.loadStocks();
        this.initCharts();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        const stockSelect = document.getElementById('stock-select');
        const startBtn = document.getElementById('start-monitor-btn');
        const stopBtn = document.getElementById('stop-monitor-btn');

        // 股票选择变化
        stockSelect.addEventListener('change', () => {
            startBtn.disabled = !stockSelect.value;
            if (this.monitoringInterval) {
                this.stopMonitoring();
            }
        });

        // 开始监控
        startBtn.addEventListener('click', () => {
            this.startMonitoring();
        });

        // 停止监控
        stopBtn.addEventListener('click', () => {
            this.stopMonitoring();
        });
    }

    /**
     * 加载股票列表
     */
    async loadStocks() {
        try {
            const response = await fetch('http://localhost:5000/api/realtime/stocks');
            const data = await response.json();

            if (data.success) {
                this.stocksData = data.data;
                this.populateStockSelect(data.data);
            } else {
                console.error('加载股票列表失败:', data.error);
            }
        } catch (error) {
            console.error('加载股票列表失败:', error);
        }
    }

    /**
     * 填充股票选择框
     */
    populateStockSelect(stocks) {
        const stockSelect = document.getElementById('stock-select');
        
        // 清空现有选项
        stockSelect.innerHTML = '<option value="">请选择股票...</option>';

        // 按分类分组
        const categories = {};
        stocks.forEach(stock => {
            if (!categories[stock.category]) {
                categories[stock.category] = [];
            }
            categories[stock.category].push(stock);
        });

        // 添加分组选项
        Object.keys(categories).forEach(category => {
            const optgroup = document.createElement('optgroup');
            optgroup.label = category;

            categories[category].forEach(stock => {
                const option = document.createElement('option');
                option.value = stock.code;
                option.textContent = `${stock.name} (${stock.code})`;
                optgroup.appendChild(option);
            });

            stockSelect.appendChild(optgroup);
        });
    }

    /**
     * 初始化图表
     */
    initCharts() {
        this.initPriceChart();
        this.initVolumeChart();
        this.initValuationChart();
    }

    /**
     * 初始化价格图表
     */
    initPriceChart() {
        const ctx = document.getElementById('price-chart').getContext('2d');
        this.charts.price = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: '当前价格',
                        type: 'bar',
                        data: [],
                        backgroundColor: 'rgba(239, 68, 68, 0.6)',
                        borderColor: '#ef4444',
                        borderWidth: 1,
                        yAxisID: 'y-price'
                    },
                    {
                        label: '涨跌幅',
                        data: [],
                        borderColor: '#22c55e',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1,
                        yAxisID: 'y-change'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            color: '#94a3b8',
                            font: { size: 11 }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#94a3b8',
                            maxTicksLimit: 8,
                            maxRotation: 45,
                            minRotation: 45
                        },
                        grid: { color: 'rgba(148, 163, 184, 0.1)' }
                    },
                    'y-price': {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        ticks: {
                            color: '#ef4444',
                            callback: function(value) {
                                return '¥' + value.toFixed(2);
                            }
                        },
                        grid: { color: 'rgba(239, 68, 68, 0.1)' }
                    },
                    'y-change': {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        ticks: {
                            color: '#22c55e',
                            callback: function(value) {
                                return value.toFixed(2) + '%';
                            }
                        },
                        grid: { drawOnChartArea: false }
                    }
                }
            }
        });
    }

    /**
     * 初始化成交量图表
     */
    initVolumeChart() {
        const ctx = document.getElementById('volume-chart').getContext('2d');
        this.charts.volume = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: '成交量',
                        data: [],
                        borderColor: '#8b5cf6',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1,
                        yAxisID: 'y-volume'
                    },
                    {
                        label: '成交额',
                        type: 'bar',
                        data: [],
                        backgroundColor: 'rgba(251, 146, 60, 0.6)',
                        borderColor: '#fb923c',
                        borderWidth: 1,
                        yAxisID: 'y-turnover'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            color: '#94a3b8',
                            font: { size: 11 }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#94a3b8',
                            maxTicksLimit: 8,
                            maxRotation: 45,
                            minRotation: 45
                        },
                        grid: { color: 'rgba(148, 163, 184, 0.1)' }
                    },
                    'y-volume': {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        ticks: {
                            color: '#8b5cf6',
                            callback: function(value) {
                                return (value / 10000).toFixed(1) + '万手';
                            }
                        },
                        grid: { color: 'rgba(139, 92, 246, 0.1)' }
                    },
                    'y-turnover': {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        ticks: {
                            color: '#fb923c',
                            callback: function(value) {
                                return (value / 10000).toFixed(1) + '万元';
                            }
                        },
                        grid: { drawOnChartArea: false }
                    }
                }
            }
        });
    }

    /**
     * 初始化估值图表
     */
    initValuationChart() {
        const ctx = document.getElementById('valuation-chart').getContext('2d');
        this.charts.valuation = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: '换手率',
                        data: [],
                        borderColor: '#06b6d4',
                        backgroundColor: 'rgba(6, 182, 212, 0.1)',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        fill: false,
                        tension: 0.1,
                        yAxisID: 'y-turnover-rate'
                    },
                    {
                        label: '市盈率',
                        data: [],
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1,
                        yAxisID: 'y-pe'
                    },
                    {
                        label: '市净率',
                        data: [],
                        borderColor: '#ec4899',
                        backgroundColor: 'rgba(236, 72, 153, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1,
                        yAxisID: 'y-pb'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            color: '#94a3b8',
                            font: { size: 11 }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#94a3b8',
                            maxTicksLimit: 8,
                            maxRotation: 45,
                            minRotation: 45
                        },
                        grid: { color: 'rgba(148, 163, 184, 0.1)' }
                    },
                    'y-turnover-rate': {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        ticks: {
                            color: '#06b6d4',
                            callback: function(value) {
                                return value.toFixed(2) + '%';
                            }
                        },
                        grid: { color: 'rgba(6, 182, 212, 0.1)' }
                    },
                    'y-pe': {
                        type: 'linear',
                        display: false,
                        ticks: {
                            color: '#f59e0b',
                            callback: function(value) {
                                return value.toFixed(1);
                            }
                        }
                    },
                    'y-pb': {
                        type: 'linear',
                        display: false,
                        ticks: {
                            color: '#ec4899',
                            callback: function(value) {
                                return value.toFixed(1);
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * 开始监控
     */
    async startMonitoring() {
        const stockCode = document.getElementById('stock-select').value;
        if (!stockCode) {
            console.log('[ERROR] No stock code selected');
            return;
        }

        console.log('[STARTING] Starting monitoring for stock code:', stockCode);
        this.currentStock = stockCode;

        // 更新UI状态
        this.updateMonitoringStatus(true);

        // 清空数据缓存
        this.clearDataBuffer();

        // 启动监控表
        try {
            console.log('[REQUEST] Sending monitoring start request...');
            const response = await fetch('http://localhost:5000/api/realtime/start', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ stock_code: stockCode })
            });

            const data = await response.json();
            console.log('[RESPONSE] Monitoring start response:', data);

            if (data.success) {
                this.currentTableName = data.table_name;
                console.log('[SUCCESS] Monitoring table created successfully:', this.currentTableName);

                // 开始定时获取数据
                this.monitoringInterval = setInterval(() => {
                    console.log('[TIMER] Timer triggered, fetching data...');
                    this.fetchRealtimeData();
                }, 5000); // 每5秒获取一次数据

                // 立即获取一次数据
                console.log('[FETCH] Fetching first data immediately...');
                this.fetchRealtimeData();
            } else {
                console.error('[ERROR] Monitoring start failed:', data.error);
                this.updateMonitoringStatus(false);
            }
        } catch (error) {
            console.error('[ERROR] Monitoring start exception:', error);
            this.updateMonitoringStatus(false);
        }
    }

    /**
     * 停止监控
     */
    async stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }

        // 停止后端监控
        if (this.currentStock) {
            try {
                await fetch('http://localhost:5000/api/realtime/stop', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ stock_code: this.currentStock })
                });
            } catch (error) {
                console.error('停止监控失败:', error);
            }
        }

        this.currentStock = null;
        this.currentTableName = null;
        this.updateMonitoringStatus(false);
        this.clearDataBuffer();
        this.clearCharts();
        this.clearDataDisplay();
    }

    /**
     * 获取实时数据
     */
    async fetchRealtimeData() {
        if (!this.currentTableName) return;

        try {
            const response = await fetch(`http://localhost:5000/api/realtime/data/${this.currentTableName}`);
            const data = await response.json();

            console.log('[API] API returned data:', data);

            if (data.success && data.data && data.data.length > 0) {
                const latestData = data.data[0]; // 获取第一条数据（最新的，按时间倒序）
                console.log('[DATA] Latest data details:', latestData);
                console.log('[DATA] Data field check:', {
                    current_price: latestData.current_price,
                    change_percent: latestData.change_percent,
                    volume: latestData.volume,
                    turnover: latestData.turnover,
                    turnover_rate: latestData.turnover_rate,
                    pe_ratio: latestData.pe_ratio,
                    pb_ratio: latestData.pb_ratio
                });

                this.updateDataBuffer(latestData);
                this.updateCharts();
                this.updateDataDisplay(latestData);
            } else {
                console.log('[WARNING] No data:', data);
            }
        } catch (error) {
            console.error('获取实时数据失败:', error);
        }
    }

    /**
     * 更新数据缓存
     */
    updateDataBuffer(data) {
        // 确保使用北京时间显示
        // 将数据库时间字符串解析为北京时间
        const timeString = data.update_time;
        // 假设数据库存储的是北京时间，添加时区信息
        const beijingTimeString = timeString + '+08:00';
        const time = new Date(beijingTimeString).toLocaleTimeString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        // 添加新数据
        this.dataBuffer.times.push(time);
        this.dataBuffer.prices.push(data.current_price);
        this.dataBuffer.changes.push(data.change_percent);
        this.dataBuffer.volumes.push(data.volume);
        this.dataBuffer.turnovers.push(data.turnover);
        this.dataBuffer.turnoverRates.push(data.turnover_rate);
        this.dataBuffer.peRatios.push(data.pe_ratio);
        this.dataBuffer.pbRatios.push(data.pb_ratio);

        // 保持最多12个数据点（2分钟，每10秒一次）
        if (this.dataBuffer.times.length > this.maxDataPoints) {
            Object.keys(this.dataBuffer).forEach(key => {
                this.dataBuffer[key].shift();
            });
        }
    }

    /**
     * 更新图表
     */
    updateCharts() {
        // 更新价格图表
        this.charts.price.data.labels = [...this.dataBuffer.times];
        this.charts.price.data.datasets[0].data = [...this.dataBuffer.prices];
        this.charts.price.data.datasets[1].data = [...this.dataBuffer.changes];
        this.charts.price.update('none');

        // 更新成交量图表
        this.charts.volume.data.labels = [...this.dataBuffer.times];
        this.charts.volume.data.datasets[0].data = [...this.dataBuffer.volumes];
        this.charts.volume.data.datasets[1].data = [...this.dataBuffer.turnovers];
        this.charts.volume.update('none');

        // 更新估值图表
        this.charts.valuation.data.labels = [...this.dataBuffer.times];
        this.charts.valuation.data.datasets[0].data = [...this.dataBuffer.turnoverRates];
        this.charts.valuation.data.datasets[1].data = [...this.dataBuffer.peRatios];
        this.charts.valuation.data.datasets[2].data = [...this.dataBuffer.pbRatios];
        this.charts.valuation.update('none');
    }

    /**
     * 更新数据显示
     */
    updateDataDisplay(data) {
        try {
            console.log('[UPDATE] Starting data display update');
            console.log('[DATA] Raw data:', data);

            // 检查数据字段是否存在
            const requiredFields = ['current_price', 'change_percent', 'volume', 'turnover', 'turnover_rate', 'pe_ratio', 'pb_ratio'];
            for (const field of requiredFields) {
                if (data[field] === undefined || data[field] === null) {
                    console.warn(`[WARNING] Field ${field} missing or empty:`, data[field]);
                }
            }

            // 更新当前价格
            console.log('[PRICE] Processing current price:', data.current_price, 'type:', typeof data.current_price);
            const priceValue = Number(data.current_price).toFixed(2);
            const priceElement = document.getElementById('current-price');
            if (priceElement) {
                priceElement.textContent = priceValue;
                priceElement.className = 'data-value ' + (data.change_percent >= 0 ? 'positive' : 'negative');
                console.log('[PRICE] Current price updated successfully:', priceValue);
                console.log('[PRICE] Element content:', priceElement.textContent);
                console.log('[PRICE] Element style:', window.getComputedStyle(priceElement).display);
                console.log('[PRICE] Element position:', priceElement.getBoundingClientRect());
            } else {
                console.error('[ERROR] Cannot find current-price element');
            }

            // 更新涨跌幅
            const changePercent = Number(data.change_percent);
            const changeValue = (changePercent >= 0 ? '+' : '') + changePercent.toFixed(2);
            const changeElement = document.getElementById('change-percent');
            if (changeElement) {
                changeElement.textContent = changeValue;
                changeElement.className = 'data-value ' + (changePercent >= 0 ? 'positive' : 'negative');
                console.log('[CHANGE] Change percent updated successfully:', changeValue, 'element content:', changeElement.textContent);
            } else {
                console.error('[ERROR] Cannot find change-percent element');
            }

            // 更新成交量（万手）
            const volumeValue = (Number(data.volume) / 10000).toFixed(1);
            const volumeElement = document.getElementById('volume');
            if (volumeElement) {
                volumeElement.textContent = volumeValue;
                console.log('[VOLUME] Volume updated successfully:', volumeValue, '万手 (original:', data.volume, '手) element content:', volumeElement.textContent);
            } else {
                console.error('[ERROR] Cannot find volume element');
            }

            // 更新成交额（万元）
            const turnoverValue = (Number(data.turnover) / 10000).toFixed(1);
            const turnoverElement = document.getElementById('turnover');
            if (turnoverElement) {
                turnoverElement.textContent = turnoverValue;
                console.log('[TURNOVER] Turnover updated successfully:', turnoverValue, '万元 (original:', data.turnover, '元) element content:', turnoverElement.textContent);
            } else {
                console.error('[ERROR] Cannot find turnover element');
            }

            // 更新换手率
            const turnoverRateValue = Number(data.turnover_rate).toFixed(2);
            const turnoverRateElement = document.getElementById('turnover-rate');
            if (turnoverRateElement) {
                turnoverRateElement.textContent = turnoverRateValue;
                console.log('[RATE] Turnover rate updated successfully:', turnoverRateValue, 'element content:', turnoverRateElement.textContent);
            } else {
                console.error('[ERROR] Cannot find turnover-rate element');
            }

            // 更新市盈率
            const peValue = Number(data.pe_ratio).toFixed(1);
            const peElement = document.getElementById('pe-ratio');
            if (peElement) {
                peElement.textContent = peValue;
                console.log('[PE] PE ratio updated successfully:', peValue, 'element content:', peElement.textContent);
            } else {
                console.error('[ERROR] Cannot find pe-ratio element');
            }

            // 更新市净率
            const pbValue = Number(data.pb_ratio).toFixed(1);
            const pbElement = document.getElementById('pb-ratio');
            if (pbElement) {
                pbElement.textContent = pbValue;
                console.log('[PB] PB ratio updated successfully:', pbValue, 'element content:', pbElement.textContent);
            } else {
                console.error('[ERROR] Cannot find pb-ratio element');
            }

            console.log('[SUCCESS] Data display update completed');

        } catch (error) {
            console.error('[ERROR] Data display update failed:', error);
            console.error('[ERROR] Error details:', error.stack);
        }
    }

    /**
     * 更新监控状态
     */
    updateMonitoringStatus(isMonitoring) {
        const statusDot = document.getElementById('status-dot');
        const statusText = document.getElementById('status-text');
        const startBtn = document.getElementById('start-monitor-btn');
        const stopBtn = document.getElementById('stop-monitor-btn');
        const stockSelect = document.getElementById('stock-select');

        if (isMonitoring) {
            statusDot.classList.add('monitoring');
            statusText.textContent = '监控中';
            startBtn.disabled = true;
            stopBtn.disabled = false;
            stockSelect.disabled = true;
        } else {
            statusDot.classList.remove('monitoring');
            statusText.textContent = '未监控';
            startBtn.disabled = !stockSelect.value;
            stopBtn.disabled = true;
            stockSelect.disabled = false;
        }
    }

    /**
     * 清空数据缓存
     */
    clearDataBuffer() {
        Object.keys(this.dataBuffer).forEach(key => {
            this.dataBuffer[key] = [];
        });
    }

    /**
     * 清空图表
     */
    clearCharts() {
        Object.values(this.charts).forEach(chart => {
            chart.data.labels = [];
            chart.data.datasets.forEach(dataset => {
                dataset.data = [];
            });
            chart.update('none');
        });
    }

    /**
     * 清空数据显示
     */
    clearDataDisplay() {
        ['current-price', 'change-percent', 'volume', 'turnover', 'turnover-rate', 'pe-ratio', 'pb-ratio'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = '--';
                element.className = 'data-value';
                console.log(`[CLEAR] Clearing element ${id}`);
            } else {
                console.error(`[ERROR] Cannot find element when clearing: ${id}`);
            }
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('[INIT] Page loaded, starting realtime monitoring initialization');

    // 检查关键DOM元素是否存在
    const requiredElements = [
        'stock-select', 'start-monitor-btn', 'stop-monitor-btn',
        'current-price', 'change-percent', 'volume', 'turnover',
        'turnover-rate', 'pe-ratio', 'pb-ratio'
    ];

    let missingElements = [];
    for (const elementId of requiredElements) {
        if (!document.getElementById(elementId)) {
            missingElements.push(elementId);
        }
    }

    if (missingElements.length > 0) {
        console.error('[ERROR] Missing critical DOM elements:', missingElements);
    } else {
        console.log('[SUCCESS] All critical DOM elements exist');
    }

    new RealtimeMonitor();
});
