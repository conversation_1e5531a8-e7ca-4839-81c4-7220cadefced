/* 农业科技监控平台 - 主样式文件 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Arial', sans-serif;
    background: linear-gradient(135deg, #0a1929 0%, #1e3a8a 50%, #1e40af 100%);
    color: #e0f2fe;
    overflow: hidden;
    height: 100vh;
    width: 100vw;
    max-width: 100vw; /* 强制限制最大宽度 */
    max-height: 100vh; /* 强制限制最大高度 */
    margin: 0;
    padding: 0;
}

/* 防止所有元素溢出 */
* {
    box-sizing: border-box;
    max-width: 100%;
}

/* 确保数据大屏页面不溢出 */
body:has(.dashboard-container) {
    overflow: hidden !important;
    width: 100vw !important;
    max-width: 100vw !important;
}

/* ==================== 数据大屏专用字体样式 ==================== */

/* 数据大屏界面窗口标题字体缩小一倍后再增大50%，再增大30% */
.dashboard-container .window-title {
    font-size: 17.55px !important; /* 13.5px × 1.3 = 17.55px */
    margin-bottom: 12px !important;
    padding: 7px 8px !important;
    text-shadow: 0 0 8px rgba(96, 165, 250, 0.5) !important;
}

/* 数据大屏响应式字体调整 */
@media (max-width: 1400px) {
    .dashboard-container .window-title {
        font-size: 15.6px !important; /* 12px × 1.3 = 15.6px */
        padding: 7px 0 !important;
    }
}

@media (max-width: 1200px) {
    .dashboard-container .window-title {
        font-size: 10.5px !important; /* 7px × 1.5 = 10.5px */
        padding: 3px 0 !important;
        margin-bottom: 6px !important;
    }
}

@media (max-width: 1000px) {
    .dashboard-container .window-title {
        font-size: 9px !important; /* 6px × 1.5 = 9px */
        padding: 3px 0 !important;
        margin-bottom: 4px !important;
    }
}

@media (max-width: 800px) {
    .dashboard-container .window-title {
        font-size: 7.5px !important; /* 5px × 1.5 = 7.5px */
        padding: 2px 0 !important;
        margin-bottom: 3px !important;
    }
}

@media (max-width: 600px) {
    .dashboard-container .window-title {
        font-size: 6.75px !important; /* 4.5px × 1.5 = 6.75px */
        padding: 2px 0 !important;
        margin-bottom: 3px !important;
    }
}

/* 科技感背景动画 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(96, 165, 250, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(147, 197, 253, 0.05) 0%, transparent 50%);
    animation: backgroundPulse 8s ease-in-out infinite alternate;
    z-index: -1;
}

@keyframes backgroundPulse {
    0% { opacity: 0.3; }
    100% { opacity: 0.7; }
}

/* 主容器 */
.dashboard-container {
    width: 100vw;
    height: 100vh;
    display: grid;
    grid-template-rows: 80px calc(100vh - 100px); /* 精确计算main-content高度 */
    gap: 10px; /* 减少gap */
    padding: 5px; /* 进一步减少padding */
    box-sizing: border-box;
    overflow: hidden;
    max-width: 100vw; /* 防止宽度溢出 */
}

/* 顶部导航栏 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(30, 58, 138, 0.8);
    border: 2px solid #60a5fa;
    border-radius: 15px;
    padding: 0 30px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(96, 165, 250, 0.2);
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #3b82f6, #2563eb);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.logo-text {
    font-size: 28px;
    font-weight: bold;
    background: linear-gradient(45deg, #60a5fa, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 20px rgba(96, 165, 250, 0.5);
}

/* 导航按钮 */
.nav-buttons {
    display: flex;
    gap: 15px;
}

.nav-btn {
    padding: 12px 25px;
    background: linear-gradient(45deg, rgba(59, 130, 246, 0.2), rgba(96, 165, 250, 0.2));
    border: 2px solid #60a5fa;
    border-radius: 25px;
    color: #e0f2fe;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.nav-btn:hover::before {
    left: 100%;
}

.nav-btn:hover {
    background: linear-gradient(45deg, rgba(59, 130, 246, 0.4), rgba(96, 165, 250, 0.4));
    box-shadow: 0 5px 20px rgba(96, 165, 250, 0.3);
    transform: translateY(-2px);
}

.nav-btn.active {
    background: linear-gradient(45deg, #3b82f6, #2563eb);
    box-shadow: 0 5px 20px rgba(59, 130, 246, 0.5);
}

/* 主内容区域 */
.main-content {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 10px; /* 进一步减少gap */
    height: 100%;
    width: 100%;
    align-items: stretch;
    padding: 0; /* 移除内边距，防止溢出 */
    margin: 0;
    box-sizing: border-box;
    overflow: hidden; /* 防止溢出 */
    max-width: 100%; /* 确保不超出容器 */
}

/* 数据窗口通用样式 */
.data-window {
    background: rgba(30, 58, 138, 0.7);
    border: 2px solid #60a5fa;
    border-radius: 15px;
    padding: 8px; /* 进一步减少内边距 */
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(96, 165, 250, 0.15);
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
}

.data-window::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #60a5fa, #3b82f6);
    animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* 窗口标题 */
.window-title {
    font-size: 18px;
    font-weight: bold;
    color: #60a5fa;
    margin-bottom: 15px;
    text-align: center;
    text-shadow: 0 0 10px rgba(96, 165, 250, 0.5);
    flex-shrink: 0; /* 防止标题被压缩 */
    padding: 8px 10px;
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 图表容器 */
.chart-container {
    width: 100%;
    height: calc(100% - 40px); /* 减少标题占用的高度 */
    position: relative;
    overflow: hidden; /* 防止图表溢出 */
    box-sizing: border-box;
}

/* 确保图表画布不溢出 */
.chart-container canvas {
    max-width: 100% !important;
    max-height: 100% !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: contain; /* 保持比例，防止变形 */
}

/* 左侧90天数据窗口 */
.left-panel {
    grid-column: 1;
    grid-row: 1 / 3;
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
}

.volume-90d, .turnover-90d {
    flex: 1;
    min-height: 0; /* 允许flex项目缩小 */
    overflow: hidden; /* 防止内容溢出 */
    display: flex;
    flex-direction: column;
}

/* 中央扇形图窗口 */
.center-panel {
    grid-column: 2;
    grid-row: 1 / 3;
    height: 100%;
}

.market-cap-pie {
    height: 100%;
}

/* 右侧7天数据窗口 */
.right-panel {
    grid-column: 3;
    grid-row: 1 / 3;
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
}

.volume-7d, .turnover-7d {
    flex: 1;
    min-height: 0; /* 允许flex项目缩小 */
    overflow: hidden; /* 防止内容溢出 */
    display: flex;
    flex-direction: column;
}

/* 加载动画 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 16px;
    color: #60a5fa;
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #60a5fa;
    border-top: 2px solid transparent;
    border-radius: 50%;
    margin-left: 10px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .main-content {
        grid-template-columns: 1fr 1.5fr 1fr;
    }

    .chart-container {
        height: calc(100% - 35px); /* 减少标题占用高度 */
    }

    .window-title {
        font-size: 16px; /* 减小标题字体 */
        padding: 8px 0; /* 减少标题内边距 */
    }
}

@media (max-width: 1200px) {
    .dashboard-container {
        grid-template-rows: 70px calc(100vh - 85px); /* 精确计算 */
        padding: 5px; /* 进一步减少内边距 */
        gap: 3px; /* 减少gap */
        width: 100vw;
        max-width: 100vw;
    }

    .main-content {
        gap: 8px; /* 减少网格间距 */
        padding: 0;
        width: 100%;
        max-width: 100%;
    }

    .chart-container {
        height: calc(100% - 30px); /* 进一步减少标题占用高度 */
        padding: 5px; /* 减少图表容器内边距 */
    }

    .window-title {
        font-size: 14px; /* 进一步减小标题字体 */
        padding: 4px 0;
        margin-bottom: 8px;
    }

    .data-window {
        padding: 6px; /* 减少窗口内边距 */
    }

    .logo-text {
        font-size: 24px;
    }

    .nav-btn {
        padding: 8px 16px;
        font-size: 13px;
    }

    .placeholder-features {
        grid-template-columns: 1fr;
    }
}

/* 中等屏幕适配 - 数据大屏 */
@media (max-width: 1000px) {
    .dashboard-container {
        padding: 3px;
        gap: 2px;
        width: 100vw;
        max-width: 100vw;
    }

    .main-content {
        grid-template-columns: 1fr 1.2fr 1fr; /* 调整中央面板比例 */
        gap: 6px;
        padding: 0;
        width: 100%;
        max-width: 100%;
    }

    .chart-container {
        height: calc(100% - 25px);
        padding: 3px;
    }

    .window-title {
        font-size: 12px;
        padding: 3px 0;
        margin-bottom: 6px;
    }

    .data-window {
        padding: 4px;
    }

    .left-panel, .right-panel {
        gap: 12px; /* 减少左右面板内部间距 */
    }
}

/* 小屏幕适配 - 数据大屏 */
@media (max-width: 800px) {
    .dashboard-container {
        padding: 2px;
        gap: 1px;
        width: 100vw;
        max-width: 100vw;
    }

    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr 1.5fr 1fr;
        gap: 4px;
        padding: 0;
        width: 100%;
        max-width: 100%;
    }

    .left-panel {
        grid-column: 1;
        grid-row: 1;
        flex-direction: row;
        gap: 6px;
    }

    .center-panel {
        grid-column: 1;
        grid-row: 2;
    }

    .right-panel {
        grid-column: 1;
        grid-row: 3;
        flex-direction: row;
        gap: 6px;
    }

    .volume-90d, .turnover-90d,
    .volume-7d, .turnover-7d {
        flex: 1;
        min-width: 0;
    }

    .chart-container {
        height: calc(100% - 20px);
        padding: 2px;
    }

    .window-title {
        font-size: 10px;
        padding: 2px 0;
        margin-bottom: 4px;
    }

    .data-window {
        padding: 3px;
    }
}

/* 移动端适配 - 数据大屏 */
@media (max-width: 600px) {
    .dashboard-container {
        padding: 2px;
        gap: 1px;
        width: 100vw;
        max-width: 100vw;
        grid-template-rows: 60px calc(100vh - 70px);
    }

    .header {
        padding: 0 15px;
    }

    .logo-text {
        font-size: 18px;
    }

    .nav-btn {
        padding: 6px 12px;
        font-size: 11px;
    }

    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto auto auto;
        gap: 3px;
        padding: 0;
        width: 100%;
        max-width: 100%;
    }

    .left-panel, .right-panel {
        flex-direction: column;
        gap: 6px;
    }

    .left-panel {
        grid-row: 1 / 3;
    }

    .center-panel {
        grid-row: 3;
    }

    .right-panel {
        grid-row: 4 / 6;
    }

    .volume-90d, .turnover-90d,
    .volume-7d, .turnover-7d,
    .market-cap-pie {
        min-height: 180px; /* 设置最小高度 */
    }

    .chart-container {
        height: calc(100% - 15px);
        padding: 1px;
    }

    .window-title {
        font-size: 9px;
        padding: 1px 0;
        margin-bottom: 3px;
        text-align: center;
    }

    .data-window {
        padding: 2px;
    }
}

/* 实时监测页面样式 */
.realtime-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 500px;
}

.placeholder-content {
    text-align: center;
    color: #e0f2fe;
    max-width: 600px;
    padding: 40px;
    background: rgba(30, 58, 138, 0.3);
    border: 2px solid #60a5fa;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.placeholder-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    animation: pulse 2s infinite;
}

.placeholder-content h2 {
    font-size: 2rem;
    margin-bottom: 15px;
    color: #60a5fa;
    text-shadow: 0 0 10px rgba(96, 165, 250, 0.5);
}

.placeholder-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.8;
}

.placeholder-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-top: 30px;
}

.feature-item {
    padding: 15px;
    background: rgba(96, 165, 250, 0.1);
    border: 1px solid rgba(96, 165, 250, 0.3);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(96, 165, 250, 0.2);
    border-color: #60a5fa;
    transform: translateY(-2px);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 排行榜页面样式 */
/* 排行榜页面的body样式 */
body:has(.ranking-container) {
    overflow: hidden !important;
    max-width: 100vw !important;
    max-height: 100vh !important;
}

/* 重置排行榜页面的main-content样式 */
body:has(.ranking-container) .main-content,
.main-content:has(.ranking-container) {
    display: block; /* 重置网格布局 */
    height: calc(100vh - 100px); /* 减去导航栏和容器间距 */
    padding: 0; /* 移除内边距 */
    margin: 0; /* 移除外边距 */
    overflow: hidden; /* 防止溢出 */
    max-width: 100vw; /* 强制限制宽度 */
}

/* 重置预测界面的main-content样式 */
body:has(.prediction-container) .main-content,
.main-content:has(.prediction-container) {
    display: block; /* 重置网格布局 */
    height: calc(100vh - 80px); /* 减去导航栏高度 */
    padding: 0; /* 移除内边距 */
    margin: 0; /* 移除外边距 */
    overflow: hidden; /* 防止溢出 */
    width: 100%; /* 确保全宽 */
}

.ranking-container {
    display: grid;
    grid-template-columns: 1fr 1fr; /* 严格的一半一半分配 */
    grid-template-rows: 1fr;
    gap: 4px; /* 最小间距 */
    height: calc(100vh - 110px); /* 减去导航栏、容器间距和缓冲 */
    padding: 2px; /* 最小内边距 */
    width: 98vw; /* 使用百分比，更保守的宽度 */
    max-width: calc(100vw - 20px); /* 设置最大宽度限制 */
    margin: 0 auto; /* 居中显示 */
    box-sizing: border-box;
    overflow: hidden; /* 防止内容溢出 */
}

.ranking-window {
    background: rgba(30, 58, 138, 0.3);
    border: 2px solid #60a5fa;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    width: 100%; /* 占满网格分配的宽度 */
    height: 100%; /* 占满网格分配的高度 */
    min-width: 0; /* 允许缩小 */
    min-height: 0; /* 允许缩小 */
    box-sizing: border-box;
}

.window-header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 15px; /* 减少内边距 */
    background: rgba(96, 165, 250, 0.1);
    border-bottom: 1px solid rgba(96, 165, 250, 0.3);
    flex-shrink: 0; /* 防止标题被压缩 */
    min-height: 0; /* 允许压缩 */
}

.window-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    font-weight: 600;
    color: #60a5fa;
    text-shadow: 0 0 10px rgba(96, 165, 250, 0.5);
    margin: 0;
}

.title-icon {
    font-size: 1.8rem;
}



.chart-container {
    flex: 1;
    padding: 5px; /* 进一步减少内边距 */
    display: flex;
    flex-direction: column;
    min-height: 0; /* 允许flex项目缩小 */
    overflow: hidden; /* 防止内容溢出 */
}

.chart-content {
    flex: 1;
    min-height: 0; /* 允许flex项目缩小 */
    width: 100%;
    height: 100%;
    /* 移除最小高度限制，让图表完全自适应 */
}



/* 大屏幕适配 (>1600px) */
@media (min-width: 1600px) {
    .ranking-container {
        gap: 6px; /* 保持较小间距 */
        padding: 3px; /* 保持较小内边距 */
        width: 98vw; /* 使用百分比 */
        max-width: calc(100vw - 20px); /* 防止右侧溢出 */
    }

    .window-title {
        font-size: 1.8rem;
    }

    .chart-container {
        padding: 12px;
    }
}

/* 中等屏幕适配 (1200px-1600px) */
@media (min-width: 1200px) and (max-width: 1599px) {
    .ranking-container {
        gap: 6px; /* 保持较小间距 */
        padding: 3px; /* 保持较小内边距 */
        width: calc(100vw - 18px); /* 防止右侧溢出 */
    }

    .window-title {
        font-size: 1.6rem;
    }

    .chart-container {
        padding: 10px;
    }
}

/* 小屏幕适配 (768px-1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
    .ranking-container {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr 1fr;
        gap: 8px; /* 减少间距 */
        padding: 5px; /* 减少内边距 */
        height: calc(100vh - 100px);
        width: calc(100vw - 15px); /* 防止右侧溢出 */
    }

    .ranking-window {
        height: 100%;
    }

    .window-title {
        font-size: 1.4rem;
    }

    .window-header {
        padding: 15px 20px;
    }

    .chart-container {
        padding: 12px;
    }
}

/* 移动端适配 (<768px) */
@media (max-width: 767px) {
    .ranking-container {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr 1fr;
        gap: 6px; /* 减少间距 */
        padding: 4px; /* 减少内边距 */
        height: calc(100vh - 100px);
        width: calc(100vw - 12px); /* 防止右侧溢出 */
    }

    .ranking-window {
        height: 100%;
    }

    .window-title {
        font-size: 1.2rem;
    }

    .window-header {
        padding: 12px 15px;
    }

    .chart-container {
        padding: 5px; /* 减少移动端内边距 */
    }
}

/* ==================== 预测界面样式 ==================== */

/* 预测页面主容器 */
.prediction-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 80px);
    width: 100vw;
    padding: 20px;
    gap: 20px;
    overflow: hidden;
    box-sizing: border-box;
    margin: 0;
}

/* 控制面板 */
.control-panel {
    display: flex;
    align-items: center;
    gap: 30px;
    padding: 18px 25px;
    background: rgba(30, 58, 138, 0.8);
    border: 2px solid #60a5fa;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    flex-shrink: 0;
    height: 70px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.control-group.horizontal {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 15px;
}

.control-group label {
    color: #60a5fa;
    font-size: 0.9rem;
    font-weight: 600;
}

.control-select {
    padding: 8px 12px;
    background: rgba(30, 58, 138, 0.9);
    border: 1px solid #60a5fa;
    border-radius: 6px;
    color: #e0f2fe;
    font-size: 0.9rem;
    min-width: 200px;
}

.control-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.predict-btn {
    padding: 10px 20px;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: auto;
}

.predict-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.predict-btn:disabled {
    background: rgba(100, 100, 100, 0.3);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 主要预测区域 */
.main-prediction-area {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    flex: 1;
    min-height: 0;
    width: 100%;
    height: calc(100vh - 170px);
    max-height: calc(100vh - 170px);
}

/* 图表区域 */
.chart-section {
    display: flex;
    flex-direction: column;
    height: 100%;
    max-height: 100%;
    overflow: hidden;
}

/* 预测界面图表窗口 */
.prediction-container .chart-window {
    background: rgba(30, 58, 138, 0.7);
    border: 2px solid #60a5fa;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(96, 165, 250, 0.15);
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* 预测界面图表容器 */
.prediction-container .chart-container {
    flex: 1;
    padding: 20px;
    position: relative;
    height: 100%;
    max-height: 100%;
    overflow: hidden;
}

/* 预测界面图表画布 */
.prediction-container .chart-container canvas {
    width: 100% !important;
    height: 100% !important;
}

/* 图表占位符 */
.chart-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #94a3b8;
    background: rgba(30, 58, 138, 0.5);
    border-radius: 10px;
}

.chart-placeholder .placeholder-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.7;
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(30, 58, 138, 0.8);
    border-radius: 10px;
    color: #60a5fa;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(96, 165, 250, 0.3);
    border-top: 4px solid #60a5fa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 分析区域 */
.analysis-section {
    display: flex;
    flex-direction: column;
    height: 100%;
    max-height: 100%;
    overflow: hidden;
}

.analysis-window {
    background: rgba(30, 58, 138, 0.7);
    border: 2px solid #60a5fa;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(96, 165, 250, 0.15);
    display: flex;
    flex-direction: column;
    height: 100%;
    flex: 1;
}

.analysis-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    height: 100%;
    max-height: 100%;
}

/* 分析占位符 */
.analysis-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: #94a3b8;
}

.analysis-placeholder .placeholder-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.7;
}

/* 分析结果 */
.analysis-result {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.stock-info {
    padding: 15px;
    background: rgba(30, 58, 138, 0.5);
    border-radius: 8px;
    border-left: 4px solid #60a5fa;
}

.stock-info h4 {
    color: #60a5fa;
    margin-bottom: 5px;
    font-size: 1.2rem;
}

.stock-info p {
    color: #94a3b8;
    margin: 2px 0;
    font-size: 0.9rem;
}

/* 预测摘要 */
.prediction-summary {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(30, 58, 138, 0.6);
    border-radius: 6px;
}

.summary-label {
    color: #94a3b8;
    font-size: 0.9rem;
}

.summary-value {
    color: #e0f2fe;
    font-weight: 600;
    font-size: 0.95rem;
}

/* 投资建议 */
.investment-advice {
    padding: 15px;
    background: rgba(30, 58, 138, 0.6);
    border-radius: 8px;
    border: 1px solid rgba(96, 165, 250, 0.3);
}

.investment-advice h5 {
    color: #60a5fa;
    margin-bottom: 10px;
    font-size: 1rem;
}

.investment-advice p {
    color: #e0f2fe;
    line-height: 1.5;
    font-size: 0.9rem;
}

/* 免责声明 */
.disclaimer {
    padding: 12px;
    background: rgba(100, 50, 0, 0.3);
    border-radius: 6px;
    border: 1px solid rgba(251, 146, 60, 0.4);
    font-size: 0.8rem;
}

.disclaimer p {
    color: #fbbf24;
    margin: 2px 0;
}

/* 图表图例 */
.chart-legend {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 3px;
    font-size: 0.7rem;
    color: #94a3b8;
}

.legend-line {
    width: 14px;
    height: 2px;
    border-radius: 1px;
}

.legend-bar {
    width: 8px;
    height: 12px;
    border-radius: 1px;
}

.legend-line.historical {
    background: #60a5fa;
}

.legend-line.predicted {
    background: #f59e0b;
    border: 1px dashed #f59e0b;
    height: 1px;
}

.legend-line.predicted-price {
    background: #10b981;
    border: 1px dashed #10b981;
    height: 1px;
}

.legend-line.predicted-change {
    background: #dc2626;
    border: 1px dashed #dc2626;
    height: 1px;
}

.legend-bar.predicted-volume {
    background: #1e40af;
    border: 1px dashed #1e40af;
}

/* 趋势值颜色 */
.trend-up {
    color: #10b981 !important;
}

.trend-down {
    color: #ef4444 !important;
}

.risk-low {
    color: #10b981 !important;
}

.risk-medium {
    color: #f59e0b !important;
}

.risk-high {
    color: #ef4444 !important;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .prediction-container {
        padding: 15px;
        gap: 15px;
    }

    .main-prediction-area {
        gap: 15px;
        height: calc(100vh - 180px);
    }
}

@media (max-width: 1200px) {
    .main-prediction-area {
        grid-template-columns: 1fr;
        grid-template-rows: 1.2fr 1fr;
        gap: 15px;
        height: calc(100vh - 160px);
    }

    .chart-section {
        min-height: 400px;
    }

    .analysis-section {
        min-height: 300px;
    }
}

@media (max-width: 768px) {
    .prediction-container {
        padding: 10px;
        gap: 10px;
    }

    .control-panel {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
        height: auto;
        padding: 15px;
    }

    .control-group {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }

    .predict-btn {
        margin-left: 0;
    }

    .main-prediction-area {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr 1fr;
        gap: 10px;
        height: calc(100vh - 140px);
    }
}

/* ==================== 实时监控界面样式 ==================== */

/* 重置实时监控界面的main-content样式 */
body:has(.realtime-container) .main-content,
.main-content:has(.realtime-container) {
    display: block;
    height: calc(100vh - 80px);
    padding: 0;
    margin: 0;
    overflow: hidden;
    width: 100%;
}

/* 实时监控主容器 */
.realtime-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 80px);
    width: 100vw;
    padding: 15px;
    gap: 15px;
    overflow: hidden;
    box-sizing: border-box;
}

/* 控制面板 */
.realtime-container .control-panel {
    display: flex;
    align-items: center;
    gap: 25px;
    padding: 15px 25px;
    background: rgba(30, 58, 138, 0.8);
    border: 2px solid #60a5fa;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    flex-shrink: 0;
    height: 60px;
}

.realtime-container .control-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.realtime-container .control-group.stock-selection {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 15px;
}

.realtime-container .control-group label {
    color: #60a5fa;
    font-size: 0.9rem;
    font-weight: 600;
    white-space: nowrap;
}

.realtime-container .control-select {
    padding: 8px 12px;
    background: rgba(30, 58, 138, 0.9);
    border: 1px solid #60a5fa;
    border-radius: 6px;
    color: #e0f2fe;
    font-size: 0.9rem;
    min-width: 200px;
}

.realtime-container .control-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* 状态指示器 */
.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #94a3b8;
    font-size: 0.9rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6b7280;
    transition: background-color 0.3s ease;
}

.status-dot.monitoring {
    background: #3b82f6;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 监控按钮 */
.monitor-btn {
    padding: 8px 16px;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    border: none;
    border-radius: 6px;
    color: white;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.monitor-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-1px);
}

.monitor-btn:disabled {
    background: rgba(100, 100, 100, 0.3);
    cursor: not-allowed;
    transform: none;
}

.monitor-btn.stop-btn {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.monitor-btn.stop-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
}

/* 图表容器 */
.charts-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
    flex: 1;
    min-height: 0;
    height: calc(100vh - 280px);
}

.realtime-container .chart-window {
    background: rgba(30, 58, 138, 0.7);
    border: 2px solid #60a5fa;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(96, 165, 250, 0.15);
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

.realtime-container .chart-container {
    flex: 1;
    padding: 15px;
    position: relative;
    height: 100%;
    overflow: hidden;
}

.realtime-container .chart-container canvas {
    width: 100% !important;
    height: 100% !important;
}

/* 数据展示区域 */
.data-display {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 10px;
    flex-shrink: 0;
    height: 120px;
}

.data-item {
    background: rgba(30, 58, 138, 0.7);
    border: 2px solid #60a5fa;
    border-radius: 10px;
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px;
    text-align: center;
}

.data-label {
    color: #94a3b8;
    font-size: 0.75rem;
    margin-bottom: 5px;
    font-weight: 500;
}

.data-value {
    color: #e0f2fe;
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 2px;
}

.data-value.positive {
    color: #10b981;
}

.data-value.negative {
    color: #ef4444;
}

.data-unit {
    color: #94a3b8;
    font-size: 0.7rem;
}
