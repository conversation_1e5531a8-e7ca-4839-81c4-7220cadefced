#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
农业股票监控平台 - 集成服务器
同时提供API接口和静态文件服务
"""

import http.server
import socketserver
import os
import webbrowser
from threading import Timer, Thread
import socket
import sys
import subprocess
import pytz

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入API相关模块
from flask import Flask, jsonify, request
from flask_cors import CORS
import random
import pandas as pd
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text
import logging
import numpy as np
import pickle

# 导入配置
from config import DatabaseConfig, CrawlerConfig

# 简单的错误记录函数
def log_error(message, location, exception):
    logger.error(f"错误 [{location}]: {message} - {exception}")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

class DashboardAPI:
    def __init__(self):
        self.db_config = DatabaseConfig()
        self.crawler_config = CrawlerConfig()
        self.db_engine = None
        self._init_database()

    def _init_database(self):
        """初始化数据库连接"""
        try:
            mysql_config = self.db_config.MYSQL_CONFIG
            connection_string = (
                f"mysql+pymysql://{mysql_config['user']}:{mysql_config['password']}"
                f"@{mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}"
                f"?charset={mysql_config['charset']}"
            )
            self.db_engine = create_engine(connection_string, echo=False)

            # 测试连接
            with self.db_engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            logger.info("数据大屏API数据库连接初始化成功")
        except Exception as e:
            logger.error(f"数据大屏API数据库连接失败: {e}")
            self.db_engine = None

    def get_random_stock(self):
        """获取随机股票代码"""
        return random.choice(list(self.crawler_config.AGRICULTURAL_STOCKS.keys()))

    def get_stock_90d_data(self, stock_code, data_type='volume'):
        """获取股票90天数据（日级）"""
        if not self.db_engine:
            return None

        try:
            table_name = f"{stock_code}_daily"

            # 计算90天前的日期
            end_date = datetime.now()
            start_date = end_date - timedelta(days=90)

            # 查询数据
            query = f"""
            SELECT trade_date, {data_type}, stock_name
            FROM {table_name}
            WHERE trade_date >= %(start_date)s AND trade_date <= %(end_date)s
            ORDER BY trade_date ASC
            LIMIT 90
            """

            with self.db_engine.connect() as conn:
                df = pd.read_sql(query, conn, params={
                    'start_date': start_date.date(),
                    'end_date': end_date.date()
                })

            if df.empty:
                return None

            # 确保trade_date是datetime类型
            df['trade_date'] = pd.to_datetime(df['trade_date'])

            # 格式化数据
            result = {
                'stock_code': stock_code,
                'stock_name': df.iloc[0]['stock_name'],
                'dates': df['trade_date'].dt.strftime('%Y-%m-%d').tolist(),
                'values': df[data_type].fillna(0).tolist(),
                'data_type': data_type
            }

            return result

        except Exception as e:
            logger.error(f"获取{stock_code}的90天{data_type}数据失败: {e}")
            return None

    def get_stock_7d_data(self, stock_code, data_type='volume'):
        """获取股票7天数据（小时级，基于分钟数据聚合）"""
        if not self.db_engine:
            return None

        try:
            table_name = f"{stock_code}_minute"

            # 计算7天前的日期
            end_datetime = datetime.now()
            start_datetime = end_datetime - timedelta(days=7)

            # 查询并按小时聚合数据
            query = f"""
            SELECT
                DATE_FORMAT(trade_datetime, '%%Y-%%m-%%d %%H:00:00') as hour_time,
                SUM({data_type}) as {data_type},
                stock_name
            FROM {table_name}
            WHERE trade_datetime >= %(start_datetime)s AND trade_datetime <= %(end_datetime)s
            GROUP BY DATE_FORMAT(trade_datetime, '%%Y-%%m-%%d %%H:00:00'), stock_name
            ORDER BY hour_time ASC
            """

            with self.db_engine.connect() as conn:
                df = pd.read_sql(query, conn, params={
                    'start_datetime': start_datetime,
                    'end_datetime': end_datetime
                })

            if df.empty:
                return None

            # 确保hour_time格式正确
            df['hour_time'] = pd.to_datetime(df['hour_time'])

            # 格式化数据
            result = {
                'stock_code': stock_code,
                'stock_name': df.iloc[0]['stock_name'],
                'hours': df['hour_time'].dt.strftime('%Y-%m-%d %H:%M:%S').tolist(),
                'values': df[data_type].fillna(0).tolist(),
                'data_type': data_type
            }

            return result

        except Exception as e:
            logger.error(f"获取{stock_code}的7天{data_type}数据失败: {e}")
            return None

    def get_market_cap_data(self):
        """获取总市值数据"""
        if not self.db_engine:
            return None

        try:
            table_name = self.db_config.TABLE_CONFIG['table_name']

            query = f"""
            SELECT stock_code, stock_name, total_market_value
            FROM {table_name}
            WHERE total_market_value IS NOT NULL AND total_market_value > 0
            ORDER BY total_market_value DESC
            """

            with self.db_engine.connect() as conn:
                df = pd.read_sql(query, conn)

            if df.empty:
                return None

            # 格式化数据
            result = []
            for _, row in df.iterrows():
                result.append({
                    'name': f"{row['stock_name']}({row['stock_code']})",
                    'value': float(row['total_market_value']),
                    'stock_code': row['stock_code']
                })

            return result

        except Exception as e:
            logger.error(f"获取总市值数据失败: {e}")
            return None

    def get_top_gainers_data(self, limit=10):
        """获取当日涨幅前十数据"""
        if not self.db_engine:
            return None

        try:
            table_name = self.db_config.TABLE_CONFIG['table_name']

            query = f"""
            SELECT stock_code, stock_name, change_percent
            FROM {table_name}
            WHERE change_percent IS NOT NULL
            ORDER BY change_percent DESC
            LIMIT {limit}
            """

            with self.db_engine.connect() as conn:
                df = pd.read_sql(query, conn)

            if df.empty:
                return None

            # 格式化数据
            result = []
            for _, row in df.iterrows():
                result.append({
                    'name': f"{row['stock_name']}({row['stock_code']})",
                    'value': float(row['change_percent']),
                    'stock_code': row['stock_code'],
                    'stock_name': row['stock_name']
                })

            return result

        except Exception as e:
            logger.error(f"获取涨幅排行数据失败: {e}")
            return None

    def get_top_market_cap_data(self, limit=10):
        """获取总市值前十数据"""
        if not self.db_engine:
            return None

        try:
            table_name = self.db_config.TABLE_CONFIG['table_name']

            query = f"""
            SELECT stock_code, stock_name, total_market_value
            FROM {table_name}
            WHERE total_market_value IS NOT NULL AND total_market_value > 0
            ORDER BY total_market_value DESC
            LIMIT {limit}
            """

            with self.db_engine.connect() as conn:
                df = pd.read_sql(query, conn)

            if df.empty:
                return None

            # 格式化数据
            result = []
            for _, row in df.iterrows():
                result.append({
                    'name': f"{row['stock_name']}({row['stock_code']})",
                    'value': float(row['total_market_value']),
                    'stock_code': row['stock_code'],
                    'stock_name': row['stock_name']
                })

            return result

        except Exception as e:
            logger.error(f"获取市值排行数据失败: {e}")
            return None

class StockPredictor:
    """股票预测器"""

    def __init__(self):
        self.db_config = DatabaseConfig()
        self.crawler_config = CrawlerConfig()
        self.agricultural_stocks = self.crawler_config.AGRICULTURAL_STOCKS

        # 模型路径
        self.models_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'models', 'universal')

    def get_stock_data(self, stock_code, data_type='daily', limit=None):
        """获取股票数据"""
        try:
            mysql_config = self.db_config.MYSQL_CONFIG
            connection_string = (
                f"mysql+pymysql://{mysql_config['user']}:{mysql_config['password']}"
                f"@{mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}"
                f"?charset={mysql_config['charset']}"
            )
            engine = create_engine(connection_string)

            if data_type == 'daily':
                table_name = f"{stock_code}_daily"
                query = f"""
                    SELECT trade_date as datetime, close_price, volume, change_percent
                    FROM {table_name}
                    WHERE close_price IS NOT NULL
                    AND volume IS NOT NULL
                    AND change_percent IS NOT NULL
                    ORDER BY trade_date DESC
                    {f'LIMIT {limit}' if limit else ''}
                """
            else:  # minute
                table_name = f"{stock_code}_minute"
                query = f"""
                    SELECT trade_datetime as datetime, close_price, volume, change_percent
                    FROM {table_name}
                    WHERE close_price IS NOT NULL
                    AND volume IS NOT NULL
                    AND change_percent IS NOT NULL
                    ORDER BY trade_datetime DESC
                    {f'LIMIT {limit}' if limit else ''}
                """

            df = pd.read_sql(query, engine)

            if df.empty:
                return None

            # 按时间正序排列
            df = df.sort_values('datetime').reset_index(drop=True)
            return df

        except Exception as e:
            logger.error(f"获取股票数据失败: {e}")
            return None

    def load_model_and_scaler(self, model_type):
        """加载模型和标准化器"""
        try:
            # 检查TensorFlow
            try:
                import tensorflow as tf
            except ImportError as e:
                logger.error(f"TensorFlow导入失败: {e}")
                return None, None

            model_path = os.path.join(self.models_dir, f'{model_type}_model_latest.h5')
            scaler_path = os.path.join(self.models_dir, f'{model_type}_scaler_latest.pkl')

            if not os.path.exists(model_path):
                logger.error(f"模型文件不存在: {model_path}")
                return None, None

            if not os.path.exists(scaler_path):
                logger.error(f"标准化器文件不存在: {scaler_path}")
                return None, None

            # 使用自定义对象来处理旧版本的损失函数名称
            custom_objects = {
                'mse': tf.keras.losses.MeanSquaredError(),
                'mae': tf.keras.losses.MeanAbsoluteError()
            }
            model = tf.keras.models.load_model(model_path, custom_objects=custom_objects)

            with open(scaler_path, 'rb') as f:
                scaler = pickle.load(f)

            return model, scaler

        except Exception as e:
            logger.error(f"加载模型失败: {str(e)}")
            return None, None

    def predict_stock(self, stock_code, prediction_type):
        """预测股票"""
        try:
            # 验证股票代码
            if stock_code not in self.agricultural_stocks:
                return {'success': False, 'error': '无效的股票代码'}

            # 根据预测类型设置参数
            if prediction_type == '3day':
                data_type = 'daily'
                sequence_length = 30
                prediction_steps = 3
                historical_limit = 60  # 显示60天历史数据
                model_type = 'daily'
            elif prediction_type == '1hour':
                data_type = 'minute'
                sequence_length = 60
                prediction_steps = 12  # 12个5分钟间隔 = 1小时
                historical_limit = 48   # 显示4小时历史数据 (48个5分钟间隔)
                model_type = 'hourly'
            else:
                return {'success': False, 'error': '无效的预测类型'}

            # 获取历史数据
            historical_data = self.get_stock_data(stock_code, data_type, historical_limit + sequence_length)
            if historical_data is None or len(historical_data) < sequence_length:
                return {'success': False, 'error': '历史数据不足'}

            # 加载模型和标准化器
            model, scaler = self.load_model_and_scaler(model_type)
            if model is None or scaler is None:
                return {'success': False, 'error': '模型加载失败'}

            # 准备特征
            features = ['close_price', 'volume', 'change_percent']

            # 数据标准化
            scaled_data = historical_data.copy()
            scaled_data[features] = scaler.transform(historical_data[features])

            # 准备预测输入
            X = scaled_data[features].values[-sequence_length:].reshape(1, sequence_length, len(features))

            # 进行预测
            prediction = model.predict(X, verbose=0)

            # 反标准化预测结果
            prediction_reshaped = prediction.reshape(prediction_steps, len(features))
            prediction_original = scaler.inverse_transform(prediction_reshaped)

            # 生成预测时间点
            last_datetime = pd.to_datetime(historical_data['datetime'].iloc[-1])
            if data_type == 'daily':
                prediction_times = [last_datetime + timedelta(days=i+1) for i in range(prediction_steps)]
            else:
                prediction_times = [last_datetime + timedelta(minutes=(i+1)*5) for i in range(prediction_steps)]

            # 准备返回数据
            historical_display = historical_data.tail(historical_limit)

            result = {
                'success': True,
                'stock_info': {
                    'code': stock_code,
                    'name': self.agricultural_stocks[stock_code]['name'],
                    'category': self.agricultural_stocks[stock_code]['category']
                },
                'prediction_type': prediction_type,
                'historical_data': {
                    'datetime': pd.to_datetime(historical_display['datetime']).dt.strftime('%Y-%m-%d %H:%M:%S').tolist(),
                    'close_price': historical_display['close_price'].tolist(),
                    'volume': historical_display['volume'].tolist(),
                    'change_percent': historical_display['change_percent'].tolist()
                },
                'prediction_data': {
                    'datetime': [t.strftime('%Y-%m-%d %H:%M:%S') for t in prediction_times],
                    'close_price': prediction_original[:, 0].tolist(),
                    'volume': prediction_original[:, 1].tolist(),
                    'change_percent': prediction_original[:, 2].tolist()
                },
                'analysis': self.generate_analysis(historical_display, prediction_original, prediction_type)
            }

            return result

        except Exception as e:
            logger.error(f"股票预测失败: {e}")
            return {'success': False, 'error': f'预测失败: {str(e)}'}

    def generate_analysis(self, historical_data, prediction_data, prediction_type):
        """生成分析建议"""
        try:
            current_price = historical_data['close_price'].iloc[-1]
            predicted_prices = prediction_data[:, 0]

            # 计算价格变化
            price_change = predicted_prices[-1] - current_price
            price_change_percent = (price_change / current_price) * 100

            # 计算趋势
            if len(predicted_prices) > 1:
                trend = "上升" if predicted_prices[-1] > predicted_prices[0] else "下降"
            else:
                trend = "上升" if price_change > 0 else "下降"

            # 生成建议
            if prediction_type == '3day':
                time_frame = "未来3天"
            else:
                time_frame = "未来1小时"

            if abs(price_change_percent) < 1:
                risk_level = "低"
                suggestion = f"预计{time_frame}价格波动较小，适合稳健投资者持有观望"
            elif abs(price_change_percent) < 3:
                risk_level = "中"
                suggestion = f"预计{time_frame}价格有一定波动，建议密切关注市场动态"
            else:
                risk_level = "高"
                suggestion = f"预计{time_frame}价格波动较大，建议谨慎操作，控制仓位"

            return {
                'trend': trend,
                'price_change': round(price_change, 2),
                'price_change_percent': round(price_change_percent, 2),
                'risk_level': risk_level,
                'suggestion': suggestion,
                'time_frame': time_frame
            }

        except Exception as e:
            return {
                'trend': '未知',
                'price_change': 0,
                'price_change_percent': 0,
                'risk_level': '未知',
                'suggestion': '分析数据不足',
                'time_frame': prediction_type
            }

# 创建API实例
dashboard_api = DashboardAPI()
predictor = StockPredictor()

# API路由定义
@app.route('/api/random-stock-90d/<data_type>')
def get_random_stock_90d(data_type):
    """获取随机股票90天数据"""
    try:
        stock_code = dashboard_api.get_random_stock()
        data = dashboard_api.get_stock_90d_data(stock_code, data_type)

        if data:
            return jsonify({
                'success': True,
                'data': data
            })
        else:
            return jsonify({
                'success': False,
                'message': '数据获取失败'
            }), 500

    except Exception as e:
        logger.error(f"随机股票90天数据API错误: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@app.route('/api/random-stock-7d/<data_type>')
def get_random_stock_7d(data_type):
    """获取随机股票7天数据"""
    try:
        stock_code = dashboard_api.get_random_stock()
        data = dashboard_api.get_stock_7d_data(stock_code, data_type)

        if data:
            return jsonify({
                'success': True,
                'data': data
            })
        else:
            return jsonify({
                'success': False,
                'message': '数据获取失败'
            }), 500

    except Exception as e:
        logger.error(f"随机股票7天数据API错误: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@app.route('/api/market-cap')
def get_market_cap():
    """获取总市值数据"""
    try:
        data = dashboard_api.get_market_cap_data()

        if data:
            return jsonify({
                'success': True,
                'data': data
            })
        else:
            return jsonify({
                'success': False,
                'message': '数据获取失败'
            }), 500

    except Exception as e:
        logger.error(f"总市值数据API错误: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@app.route('/api/top-gainers')
def get_top_gainers():
    """获取当日涨幅前十数据"""
    try:
        data = dashboard_api.get_top_gainers_data(10)

        if data:
            return jsonify({
                'success': True,
                'data': data,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'message': '数据获取失败'
            }), 500

    except Exception as e:
        logger.error(f"涨幅排行API错误: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@app.route('/api/top-market-cap')
def get_top_market_cap():
    """获取总市值前十数据"""
    try:
        data = dashboard_api.get_top_market_cap_data(10)

        if data:
            return jsonify({
                'success': True,
                'data': data,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'message': '数据获取失败'
            }), 500

    except Exception as e:
        logger.error(f"市值排行API错误: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@app.route('/api/stocks', methods=['GET'])
def get_stocks():
    """获取所有农业股票列表"""
    try:
        stocks = []
        for code, info in predictor.agricultural_stocks.items():
            stocks.append({
                'code': code,
                'name': info['name'],
                'category': info['category'],
                'industry': info['industry']
            })

        return jsonify({
            'success': True,
            'data': stocks
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/predict', methods=['POST'])
def predict():
    """股票预测接口"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')
        prediction_type = data.get('prediction_type')  # '3day' or '1hour'

        if not stock_code or not prediction_type:
            return jsonify({
                'success': False,
                'error': '缺少必要参数'
            })

        result = predictor.predict_stock(stock_code, prediction_type)
        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

# 导入实时监控服务
try:
    from api.realtime_api import get_realtime_service
    realtime_service_available = True
except ImportError as e:
    print(f"[WARNING] Realtime monitoring service import failed: {e}")
    realtime_service_available = False

# 实时监控相关API
@app.route('/api/realtime/stocks', methods=['GET'])
def get_realtime_stocks():
    """获取可监控的股票列表"""
    try:
        stocks = []
        for code, info in predictor.agricultural_stocks.items():
            stocks.append({
                'code': code,
                'name': info['name'],
                'category': info['category'],
                'industry': info['industry']
            })

        return jsonify({
            'success': True,
            'data': stocks
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/realtime/start', methods=['POST'])
def start_realtime_monitoring():
    """启动实时监控"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')

        if not stock_code:
            return jsonify({
                'success': False,
                'error': '缺少股票代码'
            })

        # 使用实时监控服务
        if not realtime_service_available:
            return jsonify({
                'success': False,
                'error': '实时监控服务不可用'
            })

        realtime_service = get_realtime_service()
        result = realtime_service.start_monitoring(stock_code)

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/realtime/stop', methods=['POST'])
def stop_realtime_monitoring():
    """停止实时监控"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')

        # 使用实时监控服务
        if not realtime_service_available:
            return jsonify({
                'success': False,
                'error': '实时监控服务不可用'
            })

        realtime_service = get_realtime_service()
        result = realtime_service.stop_monitoring(stock_code)

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/realtime/data/<table_name>', methods=['GET'])
def get_realtime_data(table_name):
    """获取实时监控数据"""
    try:
        # 使用实时监控服务
        if not realtime_service_available:
            return jsonify({
                'success': False,
                'error': '实时监控服务不可用'
            })

        realtime_service = get_realtime_service()
        result = realtime_service.get_monitoring_data(table_name)

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/realtime/debug/<table_name>', methods=['GET'])
def debug_realtime_data(table_name):
    """调试实时监控数据 - 检查数据库实际内容"""
    try:
        if not realtime_service_available:
            return jsonify({
                'success': False,
                'error': '实时监控服务不可用'
            })

        import pymysql
        from config import DatabaseConfig

        db_config = DatabaseConfig()
        connection = pymysql.connect(**db_config.MYSQL_CONFIG)
        cursor = connection.cursor(pymysql.cursors.DictCursor)

        # 检查表是否存在
        cursor.execute("""
            SELECT COUNT(*) as table_exists
            FROM information_schema.tables
            WHERE table_schema = %s AND table_name = %s
        """, (db_config.MYSQL_CONFIG['database'], table_name))

        table_check = cursor.fetchone()

        if table_check['table_exists'] == 0:
            return jsonify({
                'success': False,
                'error': f'表 {table_name} 不存在',
                'table_name': table_name
            })

        # 获取表结构
        cursor.execute(f"DESCRIBE `{table_name}`")
        table_structure = cursor.fetchall()

        # 获取数据
        cursor.execute(f"""
            SELECT * FROM `{table_name}`
            ORDER BY update_time DESC
            LIMIT 3
        """)
        sample_data = cursor.fetchall()

        # 获取数据总数
        cursor.execute(f"SELECT COUNT(*) as total_count FROM `{table_name}`")
        count_result = cursor.fetchone()

        connection.close()

        return jsonify({
            'success': True,
            'table_name': table_name,
            'table_exists': True,
            'total_records': count_result['total_count'],
            'table_structure': table_structure,
            'sample_data': sample_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'table_name': table_name
        })

@app.route('/api/health')
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat()
    })

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.path.dirname(os.path.abspath(__file__)), **kwargs)
    
    def end_headers(self):
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

def is_port_available(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False

def find_available_port(start_port=8000, max_attempts=10):
    """查找可用端口"""
    for port in range(start_port, start_port + max_attempts):
        if is_port_available(port):
            return port
    return None

def open_browser(port):
    """延迟打开浏览器"""
    webbrowser.open(f'http://localhost:{port}/html/main_dashboard.html')

def check_time_and_run_crawlers():
    """检查当前时间，如果超过晚上6点则启动爬虫"""
    try:
        # 获取北京时间
        beijing_tz = pytz.timezone('Asia/Shanghai')
        current_time = datetime.now(beijing_tz)
        current_hour = current_time.hour

        print(f"[TIME CHECK] Current Beijing time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # 检查是否超过晚上6点（18:00）
        if current_hour >= 18:
            print(f"[AUTO CRAWLER] Current time is after 6 PM ({current_hour}:XX), starting crawlers...")

            # 获取项目根目录
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

            # 1. 启动crawler.py
            try:
                print("[AUTO CRAWLER] Starting crawler.py...")
                crawler_path = os.path.join(project_root, 'data_collection', 'crawler.py')
                result = subprocess.run([sys.executable, crawler_path],
                                      cwd=project_root,
                                      capture_output=True,
                                      text=True,
                                      timeout=300)  # 5分钟超时

                if result.returncode == 0:
                    print("[AUTO CRAWLER] crawler.py completed successfully")
                    logger.info("自动启动crawler.py成功")
                else:
                    print(f"[AUTO CRAWLER] crawler.py failed with return code: {result.returncode}")
                    if result.stderr:
                        print(f"[AUTO CRAWLER] crawler.py error: {result.stderr}")
                    logger.error(f"自动启动crawler.py失败: {result.stderr}")

            except subprocess.TimeoutExpired:
                print("[AUTO CRAWLER] crawler.py timed out after 5 minutes")
                logger.warning("crawler.py执行超时")
            except Exception as e:
                print(f"[AUTO CRAWLER] Failed to start crawler.py: {e}")
                logger.error(f"启动crawler.py失败: {e}")

            # 2. 启动historical_crawler.py的功能3
            try:
                print("[AUTO CRAWLER] Starting historical_crawler.py function 3...")
                historical_crawler_path = os.path.join(project_root, 'data_collection', 'historical_crawler.py')

                # 创建一个临时脚本来自动选择功能3
                temp_script = f"""
import sys
import os
sys.path.append(r'{project_root}')

from data_collection.historical_crawler import HistoricalCrawler

def auto_run_function_3():
    try:
        crawler = HistoricalCrawler()
        # 直接调用功能3：同时爬取分钟级和天级数据
        # 使用默认参数：分钟级7天，天级90天
        result = crawler.crawl_all_agricultural_stocks_both(minute_days=7, daily_days=90)
        if result:
            print("[SUCCESS] Historical data crawling completed!")
            return True
        else:
            print("[ERROR] Historical data crawling failed!")
            return False
    except Exception as e:
        print(f"[ERROR] Historical crawler error: {{e}}")
        return False

if __name__ == '__main__':
    auto_run_function_3()
"""

                # 写入临时脚本
                temp_script_path = os.path.join(project_root, 'temp_historical_auto.py')
                with open(temp_script_path, 'w', encoding='utf-8') as f:
                    f.write(temp_script)

                # 执行临时脚本
                result = subprocess.run([sys.executable, temp_script_path],
                                      cwd=project_root,
                                      capture_output=True,
                                      text=True,
                                      timeout=600)  # 10分钟超时

                # 清理临时脚本
                try:
                    os.remove(temp_script_path)
                except:
                    pass

                if result.returncode == 0:
                    print("[AUTO CRAWLER] historical_crawler.py function 3 completed successfully")
                    logger.info("自动启动historical_crawler.py功能3成功")
                else:
                    print(f"[AUTO CRAWLER] historical_crawler.py function 3 failed with return code: {result.returncode}")
                    if result.stderr:
                        print(f"[AUTO CRAWLER] historical_crawler.py error: {result.stderr}")
                    logger.error(f"自动启动historical_crawler.py功能3失败: {result.stderr}")

            except subprocess.TimeoutExpired:
                print("[AUTO CRAWLER] historical_crawler.py timed out after 10 minutes")
                logger.warning("historical_crawler.py执行超时")
            except Exception as e:
                print(f"[AUTO CRAWLER] Failed to start historical_crawler.py: {e}")
                logger.error(f"启动historical_crawler.py失败: {e}")

            print("[AUTO CRAWLER] All crawler tasks completed")

        else:
            print(f"[TIME CHECK] Current time is before 6 PM ({current_hour}:XX), skipping crawler auto-start")

    except Exception as e:
        print(f"[ERROR] Time check and crawler auto-start failed: {e}")
        logger.error(f"时间检查和爬虫自动启动失败: {e}")

def start_api_server():
    """启动API服务器"""
    try:
        print("[STARTING] Starting API server...")
        print("[INFO] API server address: http://localhost:5000")
        print("[INFO] API endpoints: /api/health, /api/market-cap, /api/random-stock-*, /api/top-gainers, /api/top-market-cap, /api/stocks, /api/predict")
        app.run(host='0.0.0.0', port=5000, debug=False, use_reloader=False)
    except Exception as e:
        logger.error(f"API服务器启动失败: {e}")

if __name__ == '__main__':
    print("[SYSTEM] Agricultural Stock Monitoring Platform - Integrated Server")
    print("=" * 50)

    # 检查时间并自动启动爬虫（如果需要）
    check_time_and_run_crawlers()

    # 启动API服务器（在后台线程中）
    api_thread = Thread(target=start_api_server, daemon=True)
    api_thread.start()

    # 等待API服务器启动
    import time
    time.sleep(2)

    # 查找Web服务器可用端口
    PORT = find_available_port(8000)

    if PORT is None:
        print("[ERROR] Cannot find available port (8000-8009)")
        print("[INFO] Please check if other programs are using these ports")
        exit(1)

    # 如果找到的端口不是8000，说明8000被占用了
    if PORT != 8000:
        print(f"[INFO] Port 8000 is occupied, using port {PORT}")
        print(f"[INFO] If server on port 8000 is running, please visit: http://localhost:8000/html/main_dashboard.html")

    print(f"[STARTING] Starting Web server...")
    print(f"[INFO] Web server address: http://localhost:{PORT}")
    print(f"[INFO] Main page: http://localhost:{PORT}/html/main_dashboard.html")
    print(f"[INFO] Web port: {PORT}")
    print(f"[INFO] API port: 5000")
    print("=" * 50)
    print("[SUCCESS] Both servers have been started!")
    print("[INFO] Press Ctrl+C to stop all servers")

    # 3秒后自动打开浏览器
    Timer(3.0, lambda: open_browser(PORT)).start()

    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"[SUCCESS] Web server started on port {PORT}")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n[STOPPED] All servers have been stopped")
    except OSError as e:
        if "10048" in str(e):
            print(f"[WARNING] Port {PORT} unexpectedly occupied")
            print(f"[INFO] Please restart the script to use another port")
        else:
            print(f"[ERROR] Network error: {e}")
    except Exception as e:
        print(f"[ERROR] Server startup failed: {e}")
