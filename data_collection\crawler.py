"""
农业股票爬虫
专门获取农业股票数据并保存到CSV文件
"""

import requests
import pandas as pd
import time
import json
import logging
from datetime import datetime
from fake_useragent import UserAgent
import os
import sys
import pymysql
from sqlalchemy import create_engine, text

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入配置
from config import CrawlerConfig, DatabaseConfig, log_error, LogConfig, Config

# 配置日志
def setup_logging():
    """设置日志配置，包括控制台和文件输出"""
    # 创建logger
    logger = logging.getLogger(__name__)
    logger.setLevel(getattr(logging, LogConfig.LOG_LEVEL))

    # 避免重复添加handler
    if logger.handlers:
        return logger

    # 创建格式器
    formatter = logging.Formatter(
        fmt=LogConfig.LOG_FORMAT,
        datefmt=LogConfig.DATE_FORMAT
    )

    # 1. 控制台输出
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 2. 爬虫日志文件
    try:
        from logging.handlers import RotatingFileHandler

        # 确保日志目录存在
        os.makedirs(Config.LOG_DIR, exist_ok=True)

        # 爬虫日志文件 - 写入crawler.log
        crawler_log_file = os.path.join(Config.LOG_DIR, 'crawler.log')
        file_handler = RotatingFileHandler(
            crawler_log_file,
            maxBytes=LogConfig.LOG_ROTATION['max_bytes'],
            backupCount=LogConfig.LOG_ROTATION['backup_count'],
            encoding=LogConfig.LOG_ROTATION['encoding']
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        logger.info(f"爬虫日志已设置 - 文件: {crawler_log_file}")

    except Exception as e:
        print(f"设置文件日志失败: {e}")
        log_error(f"设置文件日志失败: {e}", "crawler.setup_logging", e)

    return logger

# 初始化日志
logger = setup_logging()

class StockCrawler:
    def __init__(self):
        self.config = CrawlerConfig()
        self.db_config = DatabaseConfig()
        self.ua = UserAgent() if self.config.USE_RANDOM_UA else None
        self.session = requests.Session()
        self.session.headers.update(self.config.DEFAULT_HEADERS)

        # 从配置文件获取农业股票列表
        self.agricultural_stocks = self.config.AGRICULTURAL_STOCKS
        logger.info(f"从配置文件加载了{len(self.agricultural_stocks)}只农业股票")

        # 初始化数据库连接
        self.db_engine = None
        self._init_database()

    def _init_database(self):
        """初始化数据库连接"""
        try:
            # 创建数据库连接字符串
            mysql_config = self.db_config.MYSQL_CONFIG
            connection_string = (
                f"mysql+pymysql://{mysql_config['user']}:{mysql_config['password']}"
                f"@{mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}"
                f"?charset={mysql_config['charset']}"
            )

            # 创建数据库引擎
            self.db_engine = create_engine(connection_string, echo=False)

            # 测试连接
            with self.db_engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            logger.info("数据库连接初始化成功")

            # 初始化数据表
            self._init_table()

        except Exception as e:
            error_msg = f"数据库连接初始化失败: {e}"
            logger.error(error_msg)
            log_error(error_msg, "crawler.init_database", e)
            self.db_engine = None

    def _init_table(self):
        """初始化数据表 - 使用更新策略避免数据重复"""
        if not self.db_engine:
            logger.warning("数据库引擎未初始化，跳过表创建")
            return

        try:
            table_name = self.db_config.TABLE_CONFIG['table_name']

            with self.db_engine.connect() as conn:
                # 检查表是否存在
                check_sql = f"SHOW TABLES LIKE '{table_name}'"
                result = conn.execute(text(check_sql))
                table_exists = result.fetchone() is not None

                if table_exists:
                    logger.info(f"表 {table_name} 已存在，检查并确保唯一约束")
                    # 如果表已存在但没有唯一约束，需要添加约束
                    self._ensure_unique_constraint()
                    return

                # 创建新表（仅当表不存在时）
                create_sql = f"""
                CREATE TABLE {table_name} (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码',
                    stock_name VARCHAR(50) NOT NULL COMMENT '股票名称',
                    latest_price DECIMAL(10,2) COMMENT '最新价',
                    change_amount DECIMAL(10,2) COMMENT '涨跌额',
                    change_percent DECIMAL(8,4) COMMENT '涨跌幅',
                    volume BIGINT COMMENT '成交量',
                    turnover DECIMAL(15,2) COMMENT '成交额',
                    amplitude DECIMAL(8,4) COMMENT '振幅',
                    highest DECIMAL(10,2) COMMENT '最高价',
                    lowest DECIMAL(10,2) COMMENT '最低价',
                    open_price DECIMAL(10,2) COMMENT '今开价',
                    close_price DECIMAL(10,2) COMMENT '昨收价',
                    volume_ratio DECIMAL(8,4) COMMENT '量比',
                    turnover_rate DECIMAL(8,4) COMMENT '换手率',
                    pe_ratio DECIMAL(10,2) COMMENT '市盈率',
                    pb_ratio DECIMAL(10,2) COMMENT '市净率',
                    total_market_value DECIMAL(20,2) COMMENT '总市值',
                    circulation_market_value DECIMAL(20,2) COMMENT '流通市值',
                    industry_category VARCHAR(50) COMMENT '行业分类',
                    update_time DATETIME COMMENT '更新时间',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
                    UNIQUE KEY uk_stock_code (stock_code),
                    INDEX idx_update_time (update_time),
                    INDEX idx_updated_at (updated_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='农业股票数据表'
                """

                conn.execute(text(create_sql))
                conn.commit()
                logger.info(f"成功创建表: {table_name}")

        except Exception as e:
            error_msg = f"创建数据表失败: {e}"
            logger.error(error_msg)
            log_error(error_msg, "crawler.init_table", e)

    def _ensure_unique_constraint(self):
        """确保表有唯一约束，如果没有则添加，并清理重复数据"""
        if not self.db_engine:
            return

        try:
            table_name = self.db_config.TABLE_CONFIG['table_name']

            with self.db_engine.connect() as conn:
                # 检查是否已有唯一约束
                check_constraint_sql = f"""
                SELECT COUNT(*) as constraint_count
                FROM information_schema.table_constraints
                WHERE table_schema = DATABASE()
                AND table_name = '{table_name}'
                AND constraint_type = 'UNIQUE'
                AND constraint_name = 'uk_stock_code'
                """

                result = conn.execute(text(check_constraint_sql))
                constraint_exists = result.fetchone()[0] > 0

                if constraint_exists:
                    logger.info(f"表 {table_name} 已有唯一约束")
                    return

                logger.info(f"表 {table_name} 缺少唯一约束，开始清理重复数据并添加约束")

                # 1. 清理重复数据，保留最新的记录
                cleanup_sql = f"""
                DELETE t1 FROM {table_name} t1
                INNER JOIN {table_name} t2
                WHERE t1.stock_code = t2.stock_code
                AND t1.id < t2.id
                """

                result = conn.execute(text(cleanup_sql))
                deleted_count = result.rowcount
                logger.info(f"清理了 {deleted_count} 条重复数据")

                # 2. 添加唯一约束
                add_constraint_sql = f"""
                ALTER TABLE {table_name}
                ADD CONSTRAINT uk_stock_code UNIQUE (stock_code)
                """

                conn.execute(text(add_constraint_sql))
                conn.commit()
                logger.info(f"成功为表 {table_name} 添加唯一约束")

        except Exception as e:
            error_msg = f"确保唯一约束失败: {e}"
            logger.error(error_msg)
            log_error(error_msg, "crawler.ensure_unique_constraint", e)

    def get_random_headers(self):
        """获取随机请求头"""
        headers = {}
        if self.config.USE_RANDOM_UA and self.ua:
            headers['User-Agent'] = self.ua.random
        else:
            headers['User-Agent'] = self.config.DEFAULT_UA
        headers['Referer'] = 'https://quote.eastmoney.com/'
        return headers

    def get_agricultural_stocks(self, retry=None, delay=None):
        """
        获取农业股票数据（主要方法）
        使用知名农业公司列表获取准确的农业股票数据
        """
        retry = retry or self.config.RETRY_TIMES
        delay = delay or self.config.RETRY_DELAY

        logger.info("开始获取农业股票数据...")

        # 获取所有农业股票代码
        stock_codes = list(self.agricultural_stocks.keys())
        logger.info(f"将获取 {len(stock_codes)} 只农业股票数据...")

        return self.get_stock_data_by_codes(stock_codes, retry, delay)

    def get_stock_data_by_codes(self, stock_codes, retry=None, delay=None):
        """
        根据股票代码列表获取股票数据
        """
        retry = retry or self.config.RETRY_TIMES
        delay = delay or self.config.RETRY_DELAY

        logger.info(f"开始获取{len(stock_codes)}只指定农业股票数据...")

        # 优化批次大小：对于46只股票，使用更大的批次提高效率
        batch_size = min(getattr(self.config, 'BATCH_SIZE', 20), 30)  # 最大30只一批
        all_stocks = []

        for i in range(0, len(stock_codes), batch_size):
            batch_codes = stock_codes[i:i + batch_size]
            batch_num = i//batch_size + 1
            total_batches = (len(stock_codes) + batch_size - 1) // batch_size
            logger.info(f"处理第{batch_num}/{total_batches}批股票 ({len(batch_codes)}只)...")

            # 构造股票代码字符串
            codes_str = ','.join([f"{'1' if code.startswith('6') else '0'}.{code}" for code in batch_codes])

            # 使用配置中的API URL
            url = self.config.STOCK_API_URL

            for attempt in range(retry):
                try:
                    params = {
                        'cb': 'jQuery112404953340710317169_' + str(int(time.time() * 1000)),
                        'fltt': '2',
                        'invt': '2',
                        'fields': 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152',
                        'secids': codes_str,
                        'ut': 'bd1d9ddb04089700cf9c27f6f7426281',
                        '_': str(int(time.time() * 1000))
                    }

                    headers = self.get_random_headers()
                    response = self.session.get(url, params=params, headers=headers, timeout=self.config.REQUEST_TIMEOUT)
                    response.raise_for_status()

                    batch_data = self._parse_stock_response(response.text, batch_codes)
                    if batch_data is not None and not batch_data.empty:
                        all_stocks.append(batch_data)
                        logger.info(f"第{batch_num}批数据获取成功，获得{len(batch_data)}条记录")
                    else:
                        logger.warning(f"第{batch_num}批数据为空")
                    break

                except Exception as e:
                    error_msg = f"第{attempt + 1}次获取第{batch_num}批数据失败: {e}"
                    logger.error(error_msg)
                    log_error(error_msg, "crawler.get_stock_data", e)
                    if attempt < retry - 1:
                        # 递增延迟，但设置最大延迟时间
                        sleep_time = min(delay * (attempt + 1), 10)
                        logger.info(f"等待{sleep_time}秒后重试...")
                        time.sleep(sleep_time)

            # 批次间适当延迟，避免请求过于频繁
            if i + batch_size < len(stock_codes):  # 不是最后一批
                time.sleep(self.config.REQUEST_INTERVAL)

        if all_stocks:
            combined = pd.concat(all_stocks, ignore_index=True)
            logger.info(f"成功获取{len(combined)}只农业股票数据")
            return combined

        return None

    def _parse_stock_response(self, response_text, expected_codes):
        """解析股票数据响应"""
        try:
            # 解析JSONP响应
            json_start = response_text.find('(') + 1
            json_end = response_text.rfind(')')
            json_str = response_text[json_start:json_end]

            data = json.loads(json_str)

            if data.get('rc') == 0 and data.get('data') and data['data'].get('diff'):
                stocks = data['data']['diff']
                stock_list = []

                for stock in stocks:
                    stock_code = stock.get('f12', '')
                    stock_name = stock.get('f14', '')

                    # 只处理预期的股票代码
                    if stock_code not in expected_codes:
                        continue

                    # 使用配置中的农业公司名称
                    if stock_code in self.agricultural_stocks:
                        stock_name = self.agricultural_stocks[stock_code]['name']

                    # 数据验证：确保关键字段有效
                    latest_price = self._safe_divide(stock.get('f2', 0), 100)
                    if latest_price <= 0:
                        logger.warning(f"股票 {stock_code} 最新价无效: {latest_price}")
                        continue

                    stock_info = {
                        '股票代码': stock_code,
                        '股票名称': stock_name,
                        '最新价': latest_price,
                        '涨跌幅': self._safe_divide(stock.get('f3', 0), 100),
                        '涨跌额': self._safe_divide(stock.get('f4', 0), 100),
                        '成交量': stock.get('f5', 0),
                        '成交额': stock.get('f6', 0),
                        '振幅': self._safe_divide(stock.get('f7', 0), 100),
                        '最高': self._safe_divide(stock.get('f15', 0), 100),
                        '最低': self._safe_divide(stock.get('f16', 0), 100),
                        '今开': self._safe_divide(stock.get('f17', 0), 100),
                        '昨收': self._safe_divide(stock.get('f18', 0), 100),
                        '量比': self._safe_divide(stock.get('f10', 0), 100),
                        '换手率': self._safe_divide(stock.get('f8', 0), 100),
                        '市盈率': self._safe_divide(stock.get('f9', 0), 100),
                        '市净率': self._safe_divide(stock.get('f23', 0), 100),
                        '总市值': stock.get('f20', 0),
                        '流通市值': stock.get('f21', 0),
                        '行业分类': self._get_industry_category(stock_code),
                        '数据来源': '知名农业公司',
                        '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    stock_list.append(stock_info)

                if stock_list:
                    logger.info(f"成功解析 {len(stock_list)} 条有效股票数据")
                    return pd.DataFrame(stock_list)
                else:
                    logger.warning("未解析到有效的股票数据")

            return None

        except Exception as e:
            error_msg = f"解析股票数据失败: {e}"
            logger.error(error_msg)
            log_error(error_msg, "crawler.parse_stock_response", e)
            return None

    def _get_industry_category(self, stock_code):
        """根据股票代码获取行业分类"""
        if stock_code in self.agricultural_stocks:
            return self.agricultural_stocks[stock_code]['category']
        return '农业相关'



    def _safe_divide(self, value, divisor):
        """安全除法"""
        try:
            return value / divisor if value is not None else 0
        except (TypeError, ZeroDivisionError):
            return 0







    def save_to_mysql(self, df):
        """保存数据到MySQL数据库 - 使用UPSERT策略避免重复"""
        if df is None or df.empty:
            logger.warning("没有数据可保存到数据库")
            return False

        if not self.db_engine:
            logger.error("数据库引擎未初始化，无法保存数据")
            return False

        try:
            table_name = self.db_config.TABLE_CONFIG['table_name']

            # 准备数据，将DataFrame列名映射到数据库字段名
            db_data = df.copy()

            # 列名映射
            column_mapping = {
                '股票代码': 'stock_code',
                '股票名称': 'stock_name',
                '最新价': 'latest_price',
                '涨跌额': 'change_amount',
                '涨跌幅': 'change_percent',
                '成交量': 'volume',
                '成交额': 'turnover',
                '振幅': 'amplitude',
                '最高': 'highest',
                '最低': 'lowest',
                '今开': 'open_price',
                '昨收': 'close_price',
                '量比': 'volume_ratio',
                '换手率': 'turnover_rate',
                '市盈率': 'pe_ratio',
                '市净率': 'pb_ratio',
                '总市值': 'total_market_value',
                '流通市值': 'circulation_market_value',
                '行业分类': 'industry_category',
                '更新时间': 'update_time'
            }

            # 重命名列
            db_data = db_data.rename(columns=column_mapping)

            # 只保留数据库表中存在的列
            db_columns = list(column_mapping.values())
            db_data = db_data[[col for col in db_columns if col in db_data.columns]]

            # 使用UPSERT策略：INSERT ... ON DUPLICATE KEY UPDATE
            success_count = 0
            failed_count = 0

            with self.db_engine.connect() as conn:
                for _, row in db_data.iterrows():
                    try:
                        # 构建UPSERT SQL - 使用正确的SQLAlchemy参数格式
                        columns = list(row.index)
                        values_placeholders = ', '.join([f':{col}' for col in columns])

                        # 更新子句：排除主键和stock_code，其他字段都更新
                        update_fields = [col for col in columns if col not in ['id', 'stock_code']]
                        update_clause = ', '.join([f'{col} = VALUES({col})' for col in update_fields])

                        upsert_sql = f"""
                        INSERT INTO {table_name} ({', '.join(columns)})
                        VALUES ({values_placeholders})
                        ON DUPLICATE KEY UPDATE {update_clause}
                        """

                        # 准备参数字典
                        params = {col: row[col] for col in columns}

                        # 执行UPSERT
                        conn.execute(text(upsert_sql), params)
                        success_count += 1

                    except Exception as row_error:
                        failed_count += 1
                        logger.warning(f"保存股票 {row.get('stock_code', 'unknown')} 数据失败: {row_error}")
                        log_error(f"保存股票数据失败: {row_error}", "crawler.save_to_mysql.row", row_error)

                # 提交事务
                conn.commit()

            logger.info(f"数据保存完成 - 成功: {success_count}条, 失败: {failed_count}条")

            if success_count > 0:
                logger.info(f"成功保存{success_count}条数据到MySQL数据库表: {table_name}")
                return True
            else:
                logger.error("没有数据成功保存到数据库")
                return False

        except Exception as e:
            error_msg = f"保存数据到MySQL失败: {e}"
            logger.error(error_msg)
            log_error(error_msg, "crawler.save_to_mysql", e)
            return False

def main():
    """主函数 - 农业股票爬虫"""
    logger.info("开始运行农业股票爬虫")

    try:
        # 创建爬虫实例
        crawler = StockCrawler()

        # 获取农业股票数据
        stock_data = crawler.get_agricultural_stocks()

        if stock_data is None or stock_data.empty:
            logger.error("未获取到任何股票数据")
            print("[ERROR] 未获取到任何股票数据")
            return False

        logger.info(f"成功获取 {len(stock_data)} 只农业股票数据")

        # 保存数据到MySQL数据库
        mysql_saved = crawler.save_to_mysql(stock_data)

        if mysql_saved:
            print(f"[SUCCESS] MySQL数据已保存到数据库")
            logger.info("MySQL数据保存完成")
            return True
        else:
            print("[ERROR] MySQL数据保存失败")
            logger.error("MySQL数据保存失败")
            return False

    except Exception as e:
        error_msg = f"爬虫运行失败: {e}"
        logger.error(error_msg)
        log_error(error_msg, "crawler.main", e)
        print(f"[ERROR] 爬虫运行失败: {e}")
        return False

if __name__ == "__main__":
    main()