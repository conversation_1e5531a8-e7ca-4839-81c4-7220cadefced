#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库检查功能
"""

import sys
import os
from datetime import datetime
import pytz
import pymysql

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import DatabaseConfig

def test_database_structure():
    """测试数据库表结构"""
    print("=== 数据库表结构检查 ===")
    
    try:
        db_config = DatabaseConfig()
        connection = pymysql.connect(**db_config.MYSQL_CONFIG)
        cursor = connection.cursor()
        
        table_name = db_config.TABLE_CONFIG['table_name']
        print(f"检查表: {table_name}")
        
        # 检查表是否存在
        cursor.execute("""
            SELECT COUNT(*) as table_exists
            FROM information_schema.tables
            WHERE table_schema = %s AND table_name = %s
        """, (db_config.MYSQL_CONFIG['database'], table_name))
        
        table_check = cursor.fetchone()
        if table_check[0] == 0:
            print(f"❌ 表 {table_name} 不存在")
            connection.close()
            return False
        
        print(f"✅ 表 {table_name} 存在")
        
        # 获取表结构
        cursor.execute(f"DESCRIBE {table_name}")
        columns = cursor.fetchall()
        
        print("\n表结构:")
        for column in columns:
            print(f"  {column[0]} - {column[1]} - {column[2]} - {column[3]}")
        
        # 检查update_time字段
        update_time_exists = any(col[0] == 'update_time' for col in columns)
        if update_time_exists:
            print("✅ update_time 字段存在")
        else:
            print("❌ update_time 字段不存在")
            
        connection.close()
        return update_time_exists
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_update_time_check():
    """测试更新时间检查逻辑"""
    print("\n=== 更新时间检查测试 ===")
    
    try:
        db_config = DatabaseConfig()
        connection = pymysql.connect(**db_config.MYSQL_CONFIG)
        cursor = connection.cursor()
        
        table_name = db_config.TABLE_CONFIG['table_name']
        
        # 获取数据总数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        total_count = cursor.fetchone()[0]
        print(f"表中总记录数: {total_count}")
        
        if total_count == 0:
            print("❌ 表中没有数据")
            connection.close()
            return False
        
        # 获取最新的update_time
        cursor.execute(f"""
            SELECT MAX(update_time) as latest_update, COUNT(*) as count_with_time
            FROM {table_name}
            WHERE update_time IS NOT NULL
        """)
        
        result = cursor.fetchone()
        latest_update = result[0]
        count_with_time = result[1]
        
        print(f"有update_time的记录数: {count_with_time}")
        
        if latest_update is None:
            print("❌ 没有找到update_time数据")
            connection.close()
            return False
        
        print(f"✅ 最新更新时间: {latest_update}")
        
        # 获取北京时间（转换为naive datetime以便与数据库比较）
        beijing_tz = pytz.timezone('Asia/Shanghai')
        current_time_aware = datetime.now(beijing_tz)
        current_time = current_time_aware.replace(tzinfo=None)  # 转换为naive datetime
        today_7pm = current_time.replace(hour=19, minute=0, second=0, microsecond=0)
        
        print(f"当前北京时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"今天晚上7点: {today_7pm.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 检查逻辑
        if latest_update >= today_7pm:
            print("✅ 数据已在今天晚上7点后更新，不需要运行爬虫")
            result = True
        else:
            print("❌ 数据未在今天晚上7点后更新，需要运行爬虫")
            result = False
        
        # 显示一些样本数据
        cursor.execute(f"""
            SELECT stock_code, stock_name, update_time
            FROM {table_name}
            WHERE update_time IS NOT NULL
            ORDER BY update_time DESC
            LIMIT 5
        """)
        
        sample_data = cursor.fetchall()
        print("\n最新的5条记录:")
        for row in sample_data:
            print(f"  {row[0]} - {row[1]} - {row[2]}")
        
        connection.close()
        return result
        
    except Exception as e:
        print(f"❌ 更新时间检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🌾 农业股票监控平台 - 数据库检查测试")
    print("=" * 50)
    
    # 测试数据库结构
    structure_ok = test_database_structure()
    
    if structure_ok:
        # 测试更新时间检查
        time_check_result = test_update_time_check()
        
        print("\n=== 测试总结 ===")
        print(f"数据库表结构: {'✅ 正常' if structure_ok else '❌ 异常'}")
        print(f"更新时间检查: {'✅ 数据已更新' if time_check_result else '❌ 需要更新数据'}")
        
        print("\n=== 功能说明 ===")
        print("1. 系统会在晚上7点后检查数据库")
        print("2. 如果total_data表的update_time字段显示今天晚上7点后有更新，跳过爬虫")
        print("3. 如果没有今天晚上7点后的更新，自动启动爬虫")
        print("4. 这样避免了重复运行爬虫，提高了系统效率")
    else:
        print("\n❌ 数据库表结构异常，请先运行爬虫创建表结构")

if __name__ == '__main__':
    main()
