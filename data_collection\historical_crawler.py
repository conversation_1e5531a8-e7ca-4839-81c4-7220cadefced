"""
股票历史数据爬虫（分钟级+天级）
- 分钟级数据：过去几天内每分钟的股票变化，用于1小时预测模型训练
- 天级数据：过去3个月内每天的股票变化，用于3天预测模型训练
"""

import requests
import pandas as pd
import time
import json
import logging
from datetime import datetime, timedelta
from fake_useragent import UserAgent
import os
import sys
import pymysql
from sqlalchemy import create_engine, text

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入配置
from config import CrawlerConfig, DatabaseConfig, log_error, LogConfig, Config

# 配置日志
def setup_logging():
    """设置日志配置，包括控制台和文件输出"""
    # 创建logger
    logger = logging.getLogger(__name__)
    logger.setLevel(getattr(logging, LogConfig.LOG_LEVEL))

    # 避免重复添加handler
    if logger.handlers:
        return logger

    # 创建格式器
    formatter = logging.Formatter(
        fmt=LogConfig.LOG_FORMAT,
        datefmt=LogConfig.DATE_FORMAT
    )

    # 1. 控制台输出
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 2. 爬虫日志文件
    try:
        from logging.handlers import RotatingFileHandler

        # 确保日志目录存在
        os.makedirs(Config.LOG_DIR, exist_ok=True)

        # 爬虫日志文件 - 写入crawler.log
        crawler_log_file = os.path.join(Config.LOG_DIR, 'crawler.log')
        file_handler = RotatingFileHandler(
            crawler_log_file,
            maxBytes=LogConfig.LOG_ROTATION['max_bytes'],
            backupCount=LogConfig.LOG_ROTATION['backup_count'],
            encoding=LogConfig.LOG_ROTATION['encoding']
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        logger.info(f"历史爬虫日志已设置 - 文件: {crawler_log_file}")

    except Exception as e:
        print(f"设置文件日志失败: {e}")
        log_error(f"设置文件日志失败: {e}", "historical_crawler.setup_logging", e)

    return logger

# 初始化日志
logger = setup_logging()

class HistoricalStockCrawler:
    def __init__(self):
        self.config = CrawlerConfig()
        self.db_config = DatabaseConfig()
        self.ua = UserAgent() if self.config.USE_RANDOM_UA else None
        self.session = requests.Session()
        self.session.headers.update(self.config.DEFAULT_HEADERS)

        # 从配置文件获取农业股票列表
        self.agricultural_stocks = self.config.AGRICULTURAL_STOCKS
        logger.info(f"已加载{len(self.agricultural_stocks)}只农业股票配置")
        logger.info("历史数据爬虫初始化完成（支持分钟级+天级数据）")

        # 初始化数据库连接
        self.db_engine = None
        self._init_database()

    def _init_database(self):
        """初始化数据库连接"""
        try:
            # 创建数据库连接字符串
            mysql_config = self.db_config.MYSQL_CONFIG
            connection_string = (
                f"mysql+pymysql://{mysql_config['user']}:{mysql_config['password']}"
                f"@{mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}"
                f"?charset={mysql_config['charset']}"
            )

            # 创建数据库引擎
            self.db_engine = create_engine(connection_string, echo=False)

            # 测试连接
            with self.db_engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            logger.info("历史数据爬虫数据库连接初始化成功")

        except Exception as e:
            error_msg = f"历史数据爬虫数据库连接初始化失败: {e}"
            logger.error(error_msg)
            log_error(error_msg, "historical_crawler.init_database", e)
            self.db_engine = None

    def _create_stock_table(self, stock_code):
        """为指定股票创建数据表（如果不存在）"""
        if not self.db_engine:
            logger.warning("数据库引擎未初始化，跳过表创建")
            return False

        try:
            table_name = stock_code  # 使用股票代码作为表名

            with self.db_engine.connect() as conn:
                # 检查表是否存在
                check_sql = f"SHOW TABLES LIKE '{table_name}'"
                result = conn.execute(text(check_sql))
                table_exists = result.fetchone() is not None

                if table_exists:
                    logger.info(f"表 {table_name} 已存在，将追加数据")
                    return True

                # 创建新表（仅当表不存在时）- 分钟级数据表
                create_sql = f"""
                CREATE TABLE `{table_name}` (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码',
                    stock_name VARCHAR(50) NOT NULL COMMENT '股票名称',
                    trade_datetime DATETIME NOT NULL COMMENT '交易时间（精确到分钟）',
                    open_price DECIMAL(10,2) COMMENT '开盘价',
                    close_price DECIMAL(10,2) COMMENT '收盘价',
                    highest_price DECIMAL(10,2) COMMENT '最高价',
                    lowest_price DECIMAL(10,2) COMMENT '最低价',
                    volume BIGINT COMMENT '成交量',
                    turnover DECIMAL(15,2) COMMENT '成交额',
                    amplitude DECIMAL(8,4) COMMENT '振幅',
                    change_percent DECIMAL(8,4) COMMENT '涨跌幅',
                    change_amount DECIMAL(10,2) COMMENT '涨跌额',
                    turnover_rate DECIMAL(8,4) COMMENT '换手率',
                    crawl_time DATETIME NOT NULL COMMENT '数据爬取时间',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    INDEX idx_trade_datetime (trade_datetime),
                    INDEX idx_crawl_time (crawl_time),
                    UNIQUE KEY uk_stock_datetime (stock_code, trade_datetime)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='{stock_code}分钟级历史数据表'
                """

                conn.execute(text(create_sql))
                conn.commit()
                logger.info(f"成功创建新表: {table_name}")
                return True

        except Exception as e:
            error_msg = f"创建数据表失败: {e}"
            logger.error(error_msg)
            log_error(error_msg, "historical_crawler.create_stock_table", e)
            return False

    def get_random_headers(self):
        """获取随机请求头"""
        headers = {}
        if self.config.USE_RANDOM_UA and self.ua:
            headers['User-Agent'] = self.ua.random
        else:
            headers['User-Agent'] = self.config.DEFAULT_UA
        headers['Referer'] = 'https://quote.eastmoney.com/'
        return headers
    
    def get_stock_minute_data(self, stock_code, days=None, retry=None, delay=None):
        """
        获取股票分钟级历史数据
        stock_code: 股票代码，如 '000876'
        days: 获取天数，默认使用配置中的值
        """
        # 使用配置中的参数
        days = days or self.config.HISTORICAL_CONFIG['default_days']
        retry = retry or self.config.RETRY_TIMES
        delay = delay or self.config.RETRY_DELAY

        # 验证天数限制（分钟级数据限制更严格）
        max_days = min(self.config.HISTORICAL_CONFIG['max_days'], 7)  # 分钟级数据最多7天
        if days > max_days:
            logger.warning(f"请求天数{days}超过分钟级数据最大限制{max_days}，将使用最大值")
            days = max_days

        logger.info(f"开始获取股票 {stock_code} 近{days}天的分钟级历史数据...")

        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 构造市场代码：6开头为上海(1)，其他为深圳(0)
        market_code = "1" if stock_code.startswith('6') else "0"
        secid = f"{market_code}.{stock_code}"

        # 使用配置中的API URL
        url = self.config.HISTORICAL_API_URL

        for attempt in range(retry):
            try:
                logger.info(f"第{attempt + 1}次尝试获取分钟级历史数据...")

                # 使用配置中的参数，修改为分钟级数据
                params = {
                    'cb': 'jQuery112404953340710317169_' + str(int(time.time() * 1000)),
                    'secid': secid,
                    'ut': self.config.HISTORICAL_CONFIG['ut_token'],
                    'fields1': self.config.HISTORICAL_CONFIG['fields1'],
                    'fields2': self.config.HISTORICAL_CONFIG['fields2'],
                    'klt': '1',  # 1分钟K线
                    'fqt': self.config.HISTORICAL_CONFIG['fq_type'],
                    'beg': start_date.strftime('%Y%m%d'),
                    'end': end_date.strftime('%Y%m%d'),
                    '_': str(int(time.time() * 1000))
                }

                headers = self.get_random_headers()
                response = self.session.get(
                    url,
                    params=params,
                    headers=headers,
                    timeout=self.config.REQUEST_TIMEOUT
                )
                response.raise_for_status()

                return self._parse_minute_response(response.text, stock_code)

            except Exception as e:
                error_msg = f"第{attempt + 1}次获取分钟级历史数据失败: {e}"
                logger.error(error_msg)
                log_error(error_msg, "historical_crawler.get_minute_data", e)
                if attempt < retry - 1:
                    time.sleep(delay * (attempt + 1))

        logger.error("所有重试均失败")
        return None
    
    def _parse_minute_response(self, response_text, stock_code):
        """解析分钟级历史数据响应"""
        try:
            # 解析JSONP响应
            json_start = response_text.find('(') + 1
            json_end = response_text.rfind(')')
            json_str = response_text[json_start:json_end]

            data = json.loads(json_str)

            if data.get('rc') == 0 and data.get('data') and data['data'].get('klines'):
                klines = data['data']['klines']
                stock_name = data['data'].get('name', '')

                # 获取股票名称（优先使用配置中的名称）
                if stock_code in self.agricultural_stocks:
                    stock_name = self.agricultural_stocks[stock_code]['name']

                minute_data = []

                for kline in klines:
                    # K线数据格式：时间,开盘,收盘,最高,最低,成交量,成交额,振幅,涨跌幅,涨跌额,换手率
                    parts = kline.split(',')
                    if len(parts) >= 11:
                        # 解析时间字符串，分钟级数据格式为 "2025-06-19 09:30"
                        datetime_str = parts[0]
                        try:
                            # 尝试解析不同的时间格式
                            if len(datetime_str) == 16:  # "2025-06-19 09:30"
                                trade_datetime = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M')
                            elif len(datetime_str) == 10:  # "2025-06-19"
                                trade_datetime = datetime.strptime(datetime_str, '%Y-%m-%d')
                            else:
                                trade_datetime = datetime.strptime(datetime_str[:16], '%Y-%m-%d %H:%M')
                        except ValueError:
                            logger.warning(f"无法解析时间格式: {datetime_str}")
                            continue

                        data_point = {
                            '股票代码': stock_code,
                            '股票名称': stock_name,
                            '交易时间': trade_datetime,
                            '开盘价': float(parts[1]),
                            '收盘价': float(parts[2]),
                            '最高价': float(parts[3]),
                            '最低价': float(parts[4]),
                            '成交量': int(parts[5]),
                            '成交额': float(parts[6]),
                            '振幅': float(parts[7]),
                            '涨跌幅': float(parts[8]),
                            '涨跌额': float(parts[9]),
                            '换手率': float(parts[10]),
                            '爬取时间': datetime.now()
                        }
                        minute_data.append(data_point)

                if minute_data:
                    df = pd.DataFrame(minute_data)
                    # 按时间排序（从旧到新）
                    df = df.sort_values('交易时间')
                    logger.info(f"成功解析{len(minute_data)}条分钟级历史数据")
                    return df
                else:
                    raise ValueError("未解析到有效的分钟级历史数据")
            else:
                raise ValueError(f"API返回数据格式异常: {data.get('rc', 'unknown')}")

        except Exception as e:
            error_msg = f"解析分钟级历史数据失败: {e}"
            logger.error(error_msg)
            log_error(error_msg, "historical_crawler.parse_minute_response", e)
            return None

    def get_stock_daily_data(self, stock_code, days=None, retry=None, delay=None):
        """
        获取股票天级历史数据
        stock_code: 股票代码，如 '000876'
        days: 获取天数，默认90天（3个月）
        """
        # 使用配置中的参数，天级数据可以获取更长时间
        days = days or 90  # 默认3个月
        retry = retry or self.config.RETRY_TIMES
        delay = delay or self.config.RETRY_DELAY

        # 验证天数限制（天级数据最多3个月）
        max_days = 90
        if days > max_days:
            logger.warning(f"请求天数{days}超过天级数据最大限制{max_days}，将使用最大值")
            days = max_days

        logger.info(f"开始获取股票 {stock_code} 近{days}天的天级历史数据...")

        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 构造市场代码：6开头为上海(1)，其他为深圳(0)
        market_code = "1" if stock_code.startswith('6') else "0"
        secid = f"{market_code}.{stock_code}"

        # 使用配置中的API URL
        url = self.config.HISTORICAL_API_URL

        for attempt in range(retry):
            try:
                logger.info(f"第{attempt + 1}次尝试获取天级历史数据...")

                # 使用配置中的参数，修改为天级数据
                params = {
                    'cb': 'jQuery112404953340710317169_' + str(int(time.time() * 1000)),
                    'secid': secid,
                    'ut': self.config.HISTORICAL_CONFIG['ut_token'],
                    'fields1': self.config.HISTORICAL_CONFIG['fields1'],
                    'fields2': self.config.HISTORICAL_CONFIG['fields2'],
                    'klt': '101',  # 日K线
                    'fqt': self.config.HISTORICAL_CONFIG['fq_type'],
                    'beg': start_date.strftime('%Y%m%d'),
                    'end': end_date.strftime('%Y%m%d'),
                    '_': str(int(time.time() * 1000))
                }

                headers = self.get_random_headers()
                response = self.session.get(
                    url,
                    params=params,
                    headers=headers,
                    timeout=self.config.REQUEST_TIMEOUT
                )
                response.raise_for_status()

                return self._parse_daily_response(response.text, stock_code)

            except Exception as e:
                error_msg = f"第{attempt + 1}次获取天级历史数据失败: {e}"
                logger.error(error_msg)
                log_error(error_msg, "historical_crawler.get_daily_data", e)
                if attempt < retry - 1:
                    time.sleep(delay * (attempt + 1))

        logger.error("所有重试均失败")
        return None

    def _parse_daily_response(self, response_text, stock_code):
        """解析天级历史数据响应"""
        try:
            # 解析JSONP响应
            json_start = response_text.find('(') + 1
            json_end = response_text.rfind(')')
            json_str = response_text[json_start:json_end]

            data = json.loads(json_str)

            if data.get('rc') == 0 and data.get('data') and data['data'].get('klines'):
                klines = data['data']['klines']
                stock_name = data['data'].get('name', '')

                # 获取股票名称（优先使用配置中的名称）
                if stock_code in self.agricultural_stocks:
                    stock_name = self.agricultural_stocks[stock_code]['name']

                daily_data = []

                for kline in klines:
                    # K线数据格式：日期,开盘,收盘,最高,最低,成交量,成交额,振幅,涨跌幅,涨跌额,换手率
                    parts = kline.split(',')
                    if len(parts) >= 11:
                        # 解析日期字符串，天级数据格式为 "2025-06-19"
                        date_str = parts[0]
                        try:
                            trade_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                        except ValueError:
                            logger.warning(f"无法解析日期格式: {date_str}")
                            continue

                        data_point = {
                            '股票代码': stock_code,
                            '股票名称': stock_name,
                            '交易日期': trade_date,
                            '开盘价': float(parts[1]),
                            '收盘价': float(parts[2]),
                            '最高价': float(parts[3]),
                            '最低价': float(parts[4]),
                            '成交量': int(parts[5]),
                            '成交额': float(parts[6]),
                            '振幅': float(parts[7]),
                            '涨跌幅': float(parts[8]),
                            '涨跌额': float(parts[9]),
                            '换手率': float(parts[10]),
                            '爬取时间': datetime.now()
                        }
                        daily_data.append(data_point)

                if daily_data:
                    df = pd.DataFrame(daily_data)
                    # 按日期排序（从旧到新）
                    df = df.sort_values('交易日期')
                    logger.info(f"成功解析{len(daily_data)}条天级历史数据")
                    return df
                else:
                    raise ValueError("未解析到有效的天级历史数据")
            else:
                raise ValueError(f"API返回数据格式异常: {data.get('rc', 'unknown')}")

        except Exception as e:
            error_msg = f"解析天级历史数据失败: {e}"
            logger.error(error_msg)
            log_error(error_msg, "historical_crawler.parse_daily_response", e)
            return None

    def save_minute_data_to_mysql(self, df, stock_code):
        """保存分钟级历史数据到MySQL数据库 - 使用UPSERT策略避免重复"""
        if df is None or df.empty:
            logger.warning("没有分钟级数据可保存到数据库")
            return False

        if not self.db_engine:
            logger.error("数据库引擎未初始化，无法保存数据")
            return False

        try:
            table_name = f"{stock_code}_minute"

            # 创建分钟级数据表（如果不存在）
            if not self._create_minute_table(stock_code):
                logger.error(f"创建分钟级表失败，无法保存数据: {stock_code}")
                return False

            # 准备数据，将DataFrame列名映射到数据库字段名
            db_data = df.copy()

            # 列名映射（分钟级数据）
            column_mapping = {
                '股票代码': 'stock_code',
                '股票名称': 'stock_name',
                '交易时间': 'trade_datetime',
                '开盘价': 'open_price',
                '收盘价': 'close_price',
                '最高价': 'highest_price',
                '最低价': 'lowest_price',
                '成交量': 'volume',
                '成交额': 'turnover',
                '振幅': 'amplitude',
                '涨跌幅': 'change_percent',
                '涨跌额': 'change_amount',
                '换手率': 'turnover_rate',
                '爬取时间': 'crawl_time'
            }

            # 重命名列
            db_data = db_data.rename(columns=column_mapping)

            # 只保留数据库表中存在的列
            db_columns = list(column_mapping.values())
            db_data = db_data[[col for col in db_columns if col in db_data.columns]]

            # 使用UPSERT策略：INSERT ... ON DUPLICATE KEY UPDATE
            success_count = 0
            failed_count = 0

            with self.db_engine.connect() as conn:
                for _, row in db_data.iterrows():
                    try:
                        # 构建UPSERT SQL - 使用正确的SQLAlchemy参数格式
                        columns = list(row.index)
                        values_placeholders = ', '.join([f':{col}' for col in columns])

                        # 更新子句：排除主键和唯一键字段，其他字段都更新
                        update_fields = [col for col in columns if col not in ['id', 'stock_code', 'trade_datetime']]
                        update_clause = ', '.join([f'{col} = VALUES({col})' for col in update_fields])

                        upsert_sql = f"""
                        INSERT INTO {table_name} ({', '.join(columns)})
                        VALUES ({values_placeholders})
                        ON DUPLICATE KEY UPDATE {update_clause}
                        """

                        # 准备参数字典
                        params = {col: row[col] for col in columns}

                        # 执行UPSERT
                        conn.execute(text(upsert_sql), params)
                        success_count += 1

                    except Exception as row_error:
                        failed_count += 1
                        logger.warning(f"保存分钟级数据失败 {row.get('trade_datetime', 'unknown')}: {row_error}")
                        log_error(f"保存分钟级数据失败: {row_error}", "historical_crawler.save_minute_data_to_mysql.row", row_error)

                # 提交事务
                conn.commit()

            logger.info(f"分钟级数据保存完成 - 成功: {success_count}条, 失败: {failed_count}条")

            if success_count > 0:
                logger.info(f"成功保存{success_count}条分钟级历史数据到MySQL数据库表: {table_name}")
                return True
            else:
                logger.error("没有分钟级数据成功保存到数据库")
                return False

        except Exception as e:
            error_msg = f"保存分钟级历史数据到MySQL失败: {e}"
            logger.error(error_msg)
            log_error(error_msg, "historical_crawler.save_minute_data_to_mysql", e)
            return False

    def save_daily_data_to_mysql(self, df, stock_code):
        """保存天级历史数据到MySQL数据库 - 使用UPSERT策略避免重复"""
        if df is None or df.empty:
            logger.warning("没有天级数据可保存到数据库")
            return False

        if not self.db_engine:
            logger.error("数据库引擎未初始化，无法保存数据")
            return False

        try:
            table_name = f"{stock_code}_daily"

            # 创建天级数据表（如果不存在）
            if not self._create_daily_table(stock_code):
                logger.error(f"创建天级表失败，无法保存数据: {stock_code}")
                return False

            # 准备数据，将DataFrame列名映射到数据库字段名
            db_data = df.copy()

            # 列名映射（天级数据）
            column_mapping = {
                '股票代码': 'stock_code',
                '股票名称': 'stock_name',
                '交易日期': 'trade_date',
                '开盘价': 'open_price',
                '收盘价': 'close_price',
                '最高价': 'highest_price',
                '最低价': 'lowest_price',
                '成交量': 'volume',
                '成交额': 'turnover',
                '振幅': 'amplitude',
                '涨跌幅': 'change_percent',
                '涨跌额': 'change_amount',
                '换手率': 'turnover_rate',
                '爬取时间': 'crawl_time'
            }

            # 重命名列
            db_data = db_data.rename(columns=column_mapping)

            # 只保留数据库表中存在的列
            db_columns = list(column_mapping.values())
            db_data = db_data[[col for col in db_columns if col in db_data.columns]]

            # 使用UPSERT策略：INSERT ... ON DUPLICATE KEY UPDATE
            success_count = 0
            failed_count = 0

            with self.db_engine.connect() as conn:
                for _, row in db_data.iterrows():
                    try:
                        # 构建UPSERT SQL - 使用正确的SQLAlchemy参数格式
                        columns = list(row.index)
                        values_placeholders = ', '.join([f':{col}' for col in columns])

                        # 更新子句：排除主键和唯一键字段，其他字段都更新
                        update_fields = [col for col in columns if col not in ['id', 'stock_code', 'trade_date']]
                        update_clause = ', '.join([f'{col} = VALUES({col})' for col in update_fields])

                        upsert_sql = f"""
                        INSERT INTO {table_name} ({', '.join(columns)})
                        VALUES ({values_placeholders})
                        ON DUPLICATE KEY UPDATE {update_clause}
                        """

                        # 准备参数字典
                        params = {col: row[col] for col in columns}

                        # 执行UPSERT
                        conn.execute(text(upsert_sql), params)
                        success_count += 1

                    except Exception as row_error:
                        failed_count += 1
                        logger.warning(f"保存天级数据失败 {row.get('trade_date', 'unknown')}: {row_error}")
                        log_error(f"保存天级数据失败: {row_error}", "historical_crawler.save_daily_data_to_mysql.row", row_error)

                # 提交事务
                conn.commit()

            logger.info(f"天级数据保存完成 - 成功: {success_count}条, 失败: {failed_count}条")

            if success_count > 0:
                logger.info(f"成功保存{success_count}条天级历史数据到MySQL数据库表: {table_name}")
                return True
            else:
                logger.error("没有天级数据成功保存到数据库")
                return False

        except Exception as e:
            error_msg = f"保存天级历史数据到MySQL失败: {e}"
            logger.error(error_msg)
            log_error(error_msg, "historical_crawler.save_daily_data_to_mysql", e)
            return False





    def _create_minute_table(self, stock_code):
        """创建股票分钟级数据表（如果不存在）- 使用更新策略避免重复数据"""
        if not self.db_engine:
            logger.warning("数据库引擎未初始化，跳过分钟级表创建")
            return False

        try:
            table_name = f"{stock_code}_minute"

            with self.db_engine.connect() as conn:
                # 检查表是否存在
                check_sql = f"SHOW TABLES LIKE '{table_name}'"
                result = conn.execute(text(check_sql))
                table_exists = result.fetchone() is not None

                if table_exists:
                    logger.info(f"分钟级数据表 {table_name} 已存在，检查并确保唯一约束")
                    # 如果表已存在但没有唯一约束，需要添加约束
                    self._ensure_minute_unique_constraint(table_name)
                    return True

                # 创建分钟级数据表
                create_sql = f"""
                CREATE TABLE `{table_name}` (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码',
                    stock_name VARCHAR(50) NOT NULL COMMENT '股票名称',
                    trade_datetime DATETIME NOT NULL COMMENT '交易时间（精确到分钟）',
                    open_price DECIMAL(10,2) COMMENT '开盘价',
                    close_price DECIMAL(10,2) COMMENT '收盘价',
                    highest_price DECIMAL(10,2) COMMENT '最高价',
                    lowest_price DECIMAL(10,2) COMMENT '最低价',
                    volume BIGINT COMMENT '成交量',
                    turnover DECIMAL(15,2) COMMENT '成交额',
                    amplitude DECIMAL(8,4) COMMENT '振幅',
                    change_percent DECIMAL(8,4) COMMENT '涨跌幅',
                    change_amount DECIMAL(10,2) COMMENT '涨跌额',
                    turnover_rate DECIMAL(8,4) COMMENT '换手率',
                    crawl_time DATETIME NOT NULL COMMENT '数据爬取时间',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
                    INDEX idx_trade_datetime (trade_datetime),
                    INDEX idx_crawl_time (crawl_time),
                    INDEX idx_updated_at (updated_at),
                    UNIQUE KEY uk_stock_datetime (stock_code, trade_datetime)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='{stock_code}分钟级历史数据表'
                """

                conn.execute(text(create_sql))
                conn.commit()
                logger.info(f"成功创建分钟级数据表: {table_name}")
                return True

        except Exception as e:
            error_msg = f"创建分钟级数据表失败: {e}"
            logger.error(error_msg)
            log_error(error_msg, "historical_crawler.create_minute_table", e)
            return False

    def _create_daily_table(self, stock_code):
        """创建股票天级数据表（如果不存在）- 使用更新策略避免重复数据"""
        if not self.db_engine:
            logger.warning("数据库引擎未初始化，跳过天级表创建")
            return False

        try:
            table_name = f"{stock_code}_daily"

            with self.db_engine.connect() as conn:
                # 检查表是否存在
                check_sql = f"SHOW TABLES LIKE '{table_name}'"
                result = conn.execute(text(check_sql))
                table_exists = result.fetchone() is not None

                if table_exists:
                    logger.info(f"天级数据表 {table_name} 已存在，检查并确保唯一约束")
                    # 如果表已存在但没有唯一约束，需要添加约束
                    self._ensure_daily_unique_constraint(table_name)
                    return True

                # 创建天级数据表
                create_sql = f"""
                CREATE TABLE `{table_name}` (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码',
                    stock_name VARCHAR(50) NOT NULL COMMENT '股票名称',
                    trade_date DATE NOT NULL COMMENT '交易日期',
                    open_price DECIMAL(10,2) COMMENT '开盘价',
                    close_price DECIMAL(10,2) COMMENT '收盘价',
                    highest_price DECIMAL(10,2) COMMENT '最高价',
                    lowest_price DECIMAL(10,2) COMMENT '最低价',
                    volume BIGINT COMMENT '成交量',
                    turnover DECIMAL(15,2) COMMENT '成交额',
                    amplitude DECIMAL(8,4) COMMENT '振幅',
                    change_percent DECIMAL(8,4) COMMENT '涨跌幅',
                    change_amount DECIMAL(10,2) COMMENT '涨跌额',
                    turnover_rate DECIMAL(8,4) COMMENT '换手率',
                    crawl_time DATETIME NOT NULL COMMENT '数据爬取时间',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
                    INDEX idx_trade_date (trade_date),
                    INDEX idx_crawl_time (crawl_time),
                    INDEX idx_updated_at (updated_at),
                    UNIQUE KEY uk_stock_date (stock_code, trade_date)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='{stock_code}天级历史数据表'
                """

                conn.execute(text(create_sql))
                conn.commit()
                logger.info(f"成功创建天级数据表: {table_name}")
                return True

        except Exception as e:
            error_msg = f"创建天级数据表失败: {e}"
            logger.error(error_msg)
            log_error(error_msg, "historical_crawler.create_daily_table", e)
            return False

    def _ensure_minute_unique_constraint(self, table_name):
        """确保分钟级数据表有唯一约束，如果没有则添加，并清理重复数据"""
        if not self.db_engine:
            return

        try:
            with self.db_engine.connect() as conn:
                # 检查是否已有唯一约束
                check_constraint_sql = f"""
                SELECT COUNT(*) as constraint_count
                FROM information_schema.table_constraints
                WHERE table_schema = DATABASE()
                AND table_name = '{table_name}'
                AND constraint_type = 'UNIQUE'
                AND constraint_name = 'uk_stock_datetime'
                """

                result = conn.execute(text(check_constraint_sql))
                constraint_exists = result.fetchone()[0] > 0

                if constraint_exists:
                    logger.info(f"分钟级数据表 {table_name} 已有唯一约束")
                    return

                logger.info(f"分钟级数据表 {table_name} 缺少唯一约束，开始清理重复数据并添加约束")

                # 1. 清理重复数据，保留最新的记录
                cleanup_sql = f"""
                DELETE t1 FROM {table_name} t1
                INNER JOIN {table_name} t2
                WHERE t1.stock_code = t2.stock_code
                AND t1.trade_datetime = t2.trade_datetime
                AND t1.id < t2.id
                """

                result = conn.execute(text(cleanup_sql))
                deleted_count = result.rowcount
                logger.info(f"清理了 {deleted_count} 条重复的分钟级数据")

                # 2. 添加唯一约束
                add_constraint_sql = f"""
                ALTER TABLE {table_name}
                ADD CONSTRAINT uk_stock_datetime UNIQUE (stock_code, trade_datetime)
                """

                conn.execute(text(add_constraint_sql))
                conn.commit()
                logger.info(f"成功为分钟级数据表 {table_name} 添加唯一约束")

        except Exception as e:
            error_msg = f"确保分钟级数据表唯一约束失败: {e}"
            logger.error(error_msg)
            log_error(error_msg, "historical_crawler.ensure_minute_unique_constraint", e)

    def _ensure_daily_unique_constraint(self, table_name):
        """确保天级数据表有唯一约束，如果没有则添加，并清理重复数据"""
        if not self.db_engine:
            return

        try:
            with self.db_engine.connect() as conn:
                # 检查是否已有唯一约束
                check_constraint_sql = f"""
                SELECT COUNT(*) as constraint_count
                FROM information_schema.table_constraints
                WHERE table_schema = DATABASE()
                AND table_name = '{table_name}'
                AND constraint_type = 'UNIQUE'
                AND constraint_name = 'uk_stock_date'
                """

                result = conn.execute(text(check_constraint_sql))
                constraint_exists = result.fetchone()[0] > 0

                if constraint_exists:
                    logger.info(f"天级数据表 {table_name} 已有唯一约束")
                    return

                logger.info(f"天级数据表 {table_name} 缺少唯一约束，开始清理重复数据并添加约束")

                # 1. 清理重复数据，保留最新的记录
                cleanup_sql = f"""
                DELETE t1 FROM {table_name} t1
                INNER JOIN {table_name} t2
                WHERE t1.stock_code = t2.stock_code
                AND t1.trade_date = t2.trade_date
                AND t1.id < t2.id
                """

                result = conn.execute(text(cleanup_sql))
                deleted_count = result.rowcount
                logger.info(f"清理了 {deleted_count} 条重复的天级数据")

                # 2. 添加唯一约束
                add_constraint_sql = f"""
                ALTER TABLE {table_name}
                ADD CONSTRAINT uk_stock_date UNIQUE (stock_code, trade_date)
                """

                conn.execute(text(add_constraint_sql))
                conn.commit()
                logger.info(f"成功为天级数据表 {table_name} 添加唯一约束")

        except Exception as e:
            error_msg = f"确保天级数据表唯一约束失败: {e}"
            logger.error(error_msg)
            log_error(error_msg, "historical_crawler.ensure_daily_unique_constraint", e)

    def get_stock_info(self, stock_code):
        """获取股票基本信息"""
        if stock_code in self.agricultural_stocks:
            info = self.agricultural_stocks[stock_code]
            return {
                'code': stock_code,
                'name': info['name'],
                'category': info['category'],
                'industry': info['industry']
            }
        return {'code': stock_code, 'name': '未知', 'category': '未知', 'industry': '未知'}
    
    def list_available_stocks(self):
        """列出可用的农业股票"""
        print("\n[INFO] 可用的农业股票列表:")
        print("=" * 60)

        # 按行业分组显示
        by_industry = {}
        for code, info in self.agricultural_stocks.items():
            industry = info['industry']
            if industry not in by_industry:
                by_industry[industry] = []
            by_industry[industry].append((code, info['name'], info['category']))

        for industry, stocks in by_industry.items():
            print(f"\n[{industry}]:")
            for code, name, category in stocks:
                print(f"  {code} - {name} ({category})")

        print("=" * 60)

    def crawl_all_agricultural_stocks_minute(self, days=None):
        """功能1: 爬取所有46只农业股票的分钟级历史数据"""
        days = days or min(self.config.HISTORICAL_CONFIG['default_days'], 7)

        logger.info(f"开始爬取所有{len(self.agricultural_stocks)}只农业股票近{days}天的分钟级历史数据")
        print(f"[INFO] 开始爬取所有农业股票分钟级历史数据 (近{days}天)")
        print("=" * 60)

        success_count = 0
        failed_stocks = []

        for i, (stock_code, stock_info) in enumerate(self.agricultural_stocks.items(), 1):
            try:
                print(f"\n[{i}/{len(self.agricultural_stocks)}] 正在处理: {stock_code} {stock_info['name']}")

                # 获取分钟级历史数据
                minute_data = self.get_stock_minute_data(stock_code, days)

                if minute_data is None or minute_data.empty:
                    logger.warning(f"未获取到股票 {stock_code} 的分钟级历史数据")
                    failed_stocks.append(f"{stock_code} {stock_info['name']}")
                    continue

                print(f"  [SUCCESS] 获取到 {len(minute_data)} 条分钟级数据")

                # 保存分钟级数据到MySQL
                minute_saved = self.save_minute_data_to_mysql(minute_data, stock_code)

                if minute_saved:
                    print(f"  [SUCCESS] 分钟级数据MySQL保存成功")
                    success_count += 1
                else:
                    print(f"  [ERROR] 分钟级数据MySQL保存失败")
                    failed_stocks.append(f"{stock_code} {stock_info['name']}")

                # 添加延迟避免请求过快
                time.sleep(self.config.REQUEST_INTERVAL)

            except Exception as e:
                error_msg = f"处理股票 {stock_code} 分钟级数据时发生错误: {e}"
                logger.error(error_msg)
                log_error(error_msg, "historical_crawler.crawl_all_stocks_minute", e)
                failed_stocks.append(f"{stock_code} {stock_info['name']}")

        # 输出总结
        print(f"\n" + "=" * 60)
        print(f"[COMPLETE] 分钟级数据批量爬取完成！")
        print(f"[SUCCESS] 成功: {success_count}/{len(self.agricultural_stocks)} 只股票")

        if failed_stocks:
            print(f"[ERROR] 失败的股票:")
            for stock in failed_stocks:
                print(f"  - {stock}")

        logger.info(f"分钟级数据批量爬取完成，成功{success_count}只，失败{len(failed_stocks)}只")
        return success_count > 0

    def crawl_all_agricultural_stocks_daily(self, days=None):
        """功能2: 爬取所有46只农业股票的天级历史数据"""
        days = days or 90  # 默认3个月

        logger.info(f"开始爬取所有{len(self.agricultural_stocks)}只农业股票近{days}天的天级历史数据")
        print(f"[INFO] 开始爬取所有农业股票天级历史数据 (近{days}天)")
        print("=" * 60)

        success_count = 0
        failed_stocks = []

        for i, (stock_code, stock_info) in enumerate(self.agricultural_stocks.items(), 1):
            try:
                print(f"\n[{i}/{len(self.agricultural_stocks)}] 正在处理: {stock_code} {stock_info['name']}")

                # 获取天级历史数据
                daily_data = self.get_stock_daily_data(stock_code, days)

                if daily_data is None or daily_data.empty:
                    logger.warning(f"未获取到股票 {stock_code} 的天级历史数据")
                    failed_stocks.append(f"{stock_code} {stock_info['name']}")
                    continue

                print(f"  [SUCCESS] 获取到 {len(daily_data)} 条天级数据")

                # 保存天级数据到MySQL
                daily_saved = self.save_daily_data_to_mysql(daily_data, stock_code)

                if daily_saved:
                    print(f"  [SUCCESS] 天级数据MySQL保存成功")
                    success_count += 1
                else:
                    print(f"  [ERROR] 天级数据MySQL保存失败")
                    failed_stocks.append(f"{stock_code} {stock_info['name']}")

                # 添加延迟避免请求过快
                time.sleep(self.config.REQUEST_INTERVAL)

            except Exception as e:
                error_msg = f"处理股票 {stock_code} 天级数据时发生错误: {e}"
                logger.error(error_msg)
                log_error(error_msg, "historical_crawler.crawl_all_stocks_daily", e)
                failed_stocks.append(f"{stock_code} {stock_info['name']}")

        # 输出总结
        print(f"\n" + "=" * 60)
        print(f"[COMPLETE] 天级数据批量爬取完成！")
        print(f"[SUCCESS] 成功: {success_count}/{len(self.agricultural_stocks)} 只股票")

        if failed_stocks:
            print(f"[ERROR] 失败的股票:")
            for stock in failed_stocks:
                print(f"  - {stock}")

        logger.info(f"天级数据批量爬取完成，成功{success_count}只，失败{len(failed_stocks)}只")
        return success_count > 0

    def crawl_all_agricultural_stocks_both(self, minute_days=None, daily_days=None):
        """功能3: 同时爬取所有46只农业股票的分钟级和天级历史数据"""
        minute_days = minute_days or 7  # 分钟级数据默认7天
        daily_days = daily_days or 90   # 天级数据默认90天

        logger.info(f"开始同时爬取所有{len(self.agricultural_stocks)}只农业股票的分钟级({minute_days}天)和天级({daily_days}天)历史数据")
        print(f"[INFO] 开始爬取所有农业股票历史数据")
        print(f"[INFO] 分钟级数据: 近{minute_days}天")
        print(f"[INFO] 天级数据: 近{daily_days}天")
        print("=" * 60)

        success_count = 0
        failed_stocks = []

        for i, (stock_code, stock_info) in enumerate(self.agricultural_stocks.items(), 1):
            try:
                print(f"\n[{i}/{len(self.agricultural_stocks)}] 正在处理: {stock_code} {stock_info['name']}")

                minute_success = False
                daily_success = False

                # 获取分钟级历史数据
                minute_data = self.get_stock_minute_data(stock_code, minute_days)
                if minute_data is not None and not minute_data.empty:
                    print(f"  [SUCCESS] 获取到 {len(minute_data)} 条分钟级数据")
                    minute_success = self.save_minute_data_to_mysql(minute_data, stock_code)
                    if minute_success:
                        print(f"  [SUCCESS] 分钟级数据保存成功")
                    else:
                        print(f"  [ERROR] 分钟级数据保存失败")
                else:
                    print(f"  [ERROR] 分钟级数据获取失败")

                # 获取天级历史数据
                daily_data = self.get_stock_daily_data(stock_code, daily_days)
                if daily_data is not None and not daily_data.empty:
                    print(f"  [SUCCESS] 获取到 {len(daily_data)} 条天级数据")
                    daily_success = self.save_daily_data_to_mysql(daily_data, stock_code)
                    if daily_success:
                        print(f"  [SUCCESS] 天级数据保存成功")
                    else:
                        print(f"  [ERROR] 天级数据保存失败")
                else:
                    print(f"  [ERROR] 天级数据获取失败")

                # 只要有一种数据成功就算成功
                if minute_success or daily_success:
                    success_count += 1
                else:
                    failed_stocks.append(f"{stock_code} {stock_info['name']}")

                # 添加延迟避免请求过快
                time.sleep(self.config.REQUEST_INTERVAL)

            except Exception as e:
                error_msg = f"处理股票 {stock_code} 时发生错误: {e}"
                logger.error(error_msg)
                log_error(error_msg, "historical_crawler.crawl_all_stocks_both", e)
                failed_stocks.append(f"{stock_code} {stock_info['name']}")

        # 输出总结
        print(f"\n" + "=" * 60)
        print(f"[COMPLETE] 历史数据批量爬取完成！")
        print(f"[SUCCESS] 成功: {success_count}/{len(self.agricultural_stocks)} 只股票")

        if failed_stocks:
            print(f"[ERROR] 失败的股票:")
            for stock in failed_stocks:
                print(f"  - {stock}")

        logger.info(f"历史数据批量爬取完成，成功{success_count}只，失败{len(failed_stocks)}只")
        return success_count > 0

    def crawl_single_stock_minute(self, stock_code, days=None):
        """爬取单只股票的分钟级历史数据"""
        days = days or min(self.config.HISTORICAL_CONFIG['default_days'], 7)

        logger.info(f"开始爬取股票 {stock_code} 近{days}天的分钟级历史数据")

        try:
            # 获取股票信息
            stock_info = self.get_stock_info(stock_code)
            print(f"[INFO] 股票信息: {stock_info['code']} - {stock_info['name']} ({stock_info['category']})")

            # 获取分钟级历史数据
            print(f"[INFO] 正在获取近{days}天的分钟级历史数据...")
            minute_data = self.get_stock_minute_data(stock_code, days)

            if minute_data is None or minute_data.empty:
                print("[ERROR] 未获取到分钟级历史数据")
                return False

            print(f"[SUCCESS] 成功获取 {len(minute_data)} 条分钟级历史数据")
            print(f"[INFO] 数据范围: {minute_data['交易时间'].min()} 至 {minute_data['交易时间'].max()}")

            # 显示数据预览
            print(f"\n[INFO] 数据预览 (最近5条):")
            print("=" * 100)
            preview_data = minute_data.tail(5)[['交易时间', '开盘价', '收盘价', '最高价', '最低价', '成交量', '涨跌幅']]
            print(preview_data.to_string(index=False))

            # 保存到MySQL
            mysql_saved = self.save_minute_data_to_mysql(minute_data, stock_code)

            if mysql_saved:
                print(f"\n[SUCCESS] 分钟级数据已保存到表: {stock_code}_minute")
            else:
                print(f"\n[ERROR] 分钟级数据保存失败")

            logger.info(f"单只股票 {stock_code} 分钟级数据爬取完成")
            return mysql_saved

        except Exception as e:
            error_msg = f"爬取股票 {stock_code} 分钟级数据时发生错误: {e}"
            logger.error(error_msg)
            log_error(error_msg, "historical_crawler.crawl_single_stock_minute", e)
            print(f"[ERROR] 爬取失败: {e}")
            return False

    def crawl_single_stock_daily(self, stock_code, days=None):
        """爬取单只股票的天级历史数据"""
        days = days or 90  # 默认3个月

        logger.info(f"开始爬取股票 {stock_code} 近{days}天的天级历史数据")

        try:
            # 获取股票信息
            stock_info = self.get_stock_info(stock_code)
            print(f"[INFO] 股票信息: {stock_info['code']} - {stock_info['name']} ({stock_info['category']})")

            # 获取天级历史数据
            print(f"[INFO] 正在获取近{days}天的天级历史数据...")
            daily_data = self.get_stock_daily_data(stock_code, days)

            if daily_data is None or daily_data.empty:
                print("[ERROR] 未获取到天级历史数据")
                return False

            print(f"[SUCCESS] 成功获取 {len(daily_data)} 条天级历史数据")
            print(f"[INFO] 数据范围: {daily_data['交易日期'].min()} 至 {daily_data['交易日期'].max()}")

            # 显示数据预览
            print(f"\n[INFO] 数据预览 (最近5条):")
            print("=" * 100)
            preview_data = daily_data.tail(5)[['交易日期', '开盘价', '收盘价', '最高价', '最低价', '成交量', '涨跌幅']]
            print(preview_data.to_string(index=False))

            # 保存到MySQL
            mysql_saved = self.save_daily_data_to_mysql(daily_data, stock_code)

            if mysql_saved:
                print(f"\n[SUCCESS] 天级数据已保存到表: {stock_code}_daily")
            else:
                print(f"\n[ERROR] 天级数据保存失败")

            logger.info(f"单只股票 {stock_code} 天级数据爬取完成")
            return mysql_saved

        except Exception as e:
            error_msg = f"爬取股票 {stock_code} 天级数据时发生错误: {e}"
            logger.error(error_msg)
            log_error(error_msg, "historical_crawler.crawl_single_stock_daily", e)
            print(f"[ERROR] 爬取失败: {e}")
            return False


def main():
    """主函数"""
    logger.info("开始运行股票历史数据爬虫（分钟级+天级）")
    logger.info("使用UPSERT策略避免重复数据，错误日志写入total_error.log")

    try:
        crawler = HistoricalStockCrawler()

        print("[INFO] 股票历史数据爬虫（分钟级+天级）")
        print("=" * 50)
        print("功能选择:")
        print("1. 爬取所有46只农业股票的分钟级历史数据（用于1小时预测）")
        print("2. 爬取所有46只农业股票的天级历史数据（用于3天预测）")
        print("3. 同时爬取所有46只农业股票的分钟级和天级历史数据")
        print("4. 爬取指定股票的分钟级历史数据")
        print("5. 爬取指定股票的天级历史数据")
        print("=" * 50)

        # 功能选择
        while True:
            choice = input("\n请选择功能 (1-5): ").strip()
            if choice in ['1', '2', '3', '4', '5']:
                break
            print("[ERROR] 请输入有效的选择 (1-5)")

        # 根据功能选择设置不同的天数限制
        if choice in ['1', '4']:  # 分钟级数据功能
            default_days = 7
            max_days = 7
            data_type = "分钟级"
        elif choice in ['2', '5']:  # 天级数据功能
            default_days = 90
            max_days = 90
            data_type = "天级"
        else:  # choice == '3' 同时爬取
            default_days = 7  # 对于同时爬取，使用较小的默认值
            max_days = 90     # 但允许更大的最大值
            data_type = "混合"

        while True:
            if choice == '3':
                days_input = input(f"请输入获取天数 (默认{default_days}天，分钟级最多7天，天级最多90天): ").strip()
            else:
                days_input = input(f"请输入获取天数 (默认{default_days}天，{data_type}数据最多{max_days}天): ").strip()

            if not days_input:
                days = default_days
                break
            try:
                days = int(days_input)
                if days <= 0:
                    print("[ERROR] 天数必须大于0")
                    continue
                if days > max_days:
                    print(f"[WARNING] {data_type}数据超过最大限制{max_days}天，将使用最大值")
                    days = max_days
                break
            except ValueError:
                print("[ERROR] 请输入有效的数字")

        if choice == '1':
            # 功能1: 爬取所有农业股票分钟级数据
            print(f"\n[INFO] 选择功能1: 爬取所有农业股票分钟级历史数据")
            # 分钟级数据使用较小的天数
            minute_days = min(days, 7)
            result = crawler.crawl_all_agricultural_stocks_minute(minute_days)

        elif choice == '2':
            # 功能2: 爬取所有农业股票天级数据
            print(f"\n[INFO] 选择功能2: 爬取所有农业股票天级历史数据")
            # 天级数据可以使用更大的天数
            daily_days = min(days, 90)
            result = crawler.crawl_all_agricultural_stocks_daily(daily_days)

        elif choice == '3':
            # 功能3: 同时爬取分钟级和天级数据
            print(f"\n[INFO] 选择功能3: 同时爬取分钟级和天级历史数据")
            minute_days = min(days, 7)
            daily_days = min(days, 90)
            result = crawler.crawl_all_agricultural_stocks_both(minute_days, daily_days)

        elif choice == '4':
            # 功能4: 爬取指定股票分钟级数据
            print(f"\n[INFO] 选择功能4: 爬取指定股票分钟级历史数据")

            # 显示可用股票列表
            show_list = input("是否显示可用股票列表? (y/n): ").strip().lower()
            if show_list == 'y':
                crawler.list_available_stocks()

            # 获取股票代码
            while True:
                stock_code = input("\n请输入股票代码 (如 000876): ").strip()
                if not stock_code:
                    print("[ERROR] 股票代码不能为空")
                    continue

                # 验证股票代码
                stock_info = crawler.get_stock_info(stock_code)
                print(f"[INFO] 股票信息: {stock_info['code']} - {stock_info['name']} ({stock_info['category']})")

                confirm = input("确认获取此股票分钟级数据? (y/n): ").strip().lower()
                if confirm == 'y':
                    break

            minute_days = min(days, 7)
            result = crawler.crawl_single_stock_minute(stock_code, minute_days)

        else:  # choice == '5'
            # 功能5: 爬取指定股票天级数据
            print(f"\n[INFO] 选择功能5: 爬取指定股票天级历史数据")

            # 显示可用股票列表
            show_list = input("是否显示可用股票列表? (y/n): ").strip().lower()
            if show_list == 'y':
                crawler.list_available_stocks()

            # 获取股票代码
            while True:
                stock_code = input("\n请输入股票代码 (如 000876): ").strip()
                if not stock_code:
                    print("[ERROR] 股票代码不能为空")
                    continue

                # 验证股票代码
                stock_info = crawler.get_stock_info(stock_code)
                print(f"[INFO] 股票信息: {stock_info['code']} - {stock_info['name']} ({stock_info['category']})")

                confirm = input("确认获取此股票天级数据? (y/n): ").strip().lower()
                if confirm == 'y':
                    break

            daily_days = min(days, 90)
            result = crawler.crawl_single_stock_daily(stock_code, daily_days)

        # 根据功能选择显示不同的完成消息
        if choice in ['1', '4']:
            task_type = "分钟级历史数据"
        elif choice in ['2', '5']:
            task_type = "天级历史数据"
        else:  # choice == '3'
            task_type = "历史数据（分钟级+天级）"

        if result:
            print(f"\n[SUCCESS] {task_type}爬取完成！")
            logger.info(f"{task_type}爬取任务完成")
            return True
        else:
            print(f"\n[ERROR] {task_type}爬取失败")
            logger.error(f"{task_type}爬取任务失败")
            return False

    except Exception as e:
        error_msg = f"爬虫运行失败: {e}"
        logger.error(error_msg)
        log_error(error_msg, "historical_crawler.main", e)
        print(f"[ERROR] 爬虫运行失败: {e}")
        return False

if __name__ == "__main__":
    main()
