#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理项目缓存文件脚本
删除所有 __pycache__ 目录和 .pyc 文件
"""

import os
import shutil
import sys

def clean_pycache(root_dir="."):
    """清理指定目录下的所有 __pycache__ 目录和 .pyc 文件"""
    deleted_dirs = []
    deleted_files = []
    
    for root, dirs, files in os.walk(root_dir):
        # 删除 __pycache__ 目录
        if "__pycache__" in dirs:
            pycache_path = os.path.join(root, "__pycache__")
            try:
                shutil.rmtree(pycache_path)
                deleted_dirs.append(pycache_path)
                print(f"已删除目录: {pycache_path}")
            except Exception as e:
                print(f"删除目录失败 {pycache_path}: {e}")
        
        # 删除 .pyc 文件
        for file in files:
            if file.endswith('.pyc'):
                pyc_path = os.path.join(root, file)
                try:
                    os.remove(pyc_path)
                    deleted_files.append(pyc_path)
                    print(f"已删除文件: {pyc_path}")
                except Exception as e:
                    print(f"删除文件失败 {pyc_path}: {e}")
    
    return deleted_dirs, deleted_files

def main():
    """主函数"""
    print("开始清理Python缓存文件...")
    print("=" * 50)
    
    # 获取项目根目录
    project_root = os.path.dirname(os.path.abspath(__file__))
    
    # 清理缓存
    deleted_dirs, deleted_files = clean_pycache(project_root)
    
    print("=" * 50)
    print(f"清理完成！")
    print(f"删除了 {len(deleted_dirs)} 个 __pycache__ 目录")
    print(f"删除了 {len(deleted_files)} 个 .pyc 文件")
    
    if not deleted_dirs and not deleted_files:
        print("没有找到需要清理的缓存文件")

if __name__ == "__main__":
    main()
