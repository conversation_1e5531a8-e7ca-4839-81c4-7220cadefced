import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._
import java.sql.DriverManager
import scala.util.Using

object CombinedSparkMySQLProcessor {
  def main(args: Array[String]): Unit = {
    // 数据库连接参数（提取为常量，避免重复）
    val jdbcUrl = "*************************************************"
    val username = "root"
    val password = "q20040619"
    val driver = "com.mysql.cj.jdbc.Driver"

    // 创建单个SparkSession供所有任务使用
    val spark = SparkSession.builder()
      .appName("CombinedSparkMySQLProcessor")
      .master("local[*]")
      .getOrCreate()

    try {
      // 任务1：提取数据并按总市值排序
      println("开始执行任务1：提取数据并按总市值排序...")
      val properties = new java.util.Properties()
      properties.setProperty("user", username)
      properties.setProperty("password", password)
      properties.setProperty("driver", driver)

      val df = spark.read.jdbc(jdbcUrl, "total_data", properties)
      val selectedDf = df.select("stock_code", "stock_name", "total_market_value")
      val sortedDf = selectedDf.sort(selectedDf("total_market_value").desc)
      sortedDf.write.mode("overwrite").jdbc(jdbcUrl, "allin", properties)
      println("任务1完成：已按总市值排序并保存到allin表")

      // 任务2：提取涨跌幅数据并取前10
      println("开始执行任务2：提取涨跌幅数据并取前10...")
      val changeDf = df.select("stock_code", "stock_name", "change_percent")
      val top10Df = changeDf.orderBy(desc("change_percent")).limit(10)
      top10Df.write.mode("overwrite").jdbc(jdbcUrl, "allin1", properties)
      println("任务2完成：已降序排序涨跌幅，并取前10保存到allin1")

      // 任务3：处理所有_daily和_minute表
      println("开始执行任务3：处理所有_daily和_minute表...")
      
      // 获取所有表名
      val tableNames = Using.Manager { use =>
        val connection = use(DriverManager.getConnection(jdbcUrl, username, password))
        val statement = use(connection.createStatement())
        val resultSet = use(statement.executeQuery("SHOW TABLES"))

        var tables = List[String]()
        while (resultSet.next()) {
          tables = resultSet.getString(1) :: tables
        }
        tables
      }.getOrElse {
        println("获取表名失败，退出程序")
        sys.exit(1)
      }

      // 筛选后缀为_daily和_minute的表
      val dailyTables = tableNames.filter(_.endsWith("_daily"))
      val minuteTables = tableNames.filter(_.endsWith("_minute"))

      // 处理_daily表
      dailyTables.foreach(tableName => processTable(spark, tableName, jdbcUrl, username, password, driver, isDaily = true))

      // 处理_minute表
      minuteTables.foreach(tableName => processTable(spark, tableName, jdbcUrl, username, password, driver, isDaily = false))

      println("所有任务已完成！")
    } catch {
      case e: Exception =>
        println(s"执行过程中发生错误: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      // 确保SparkSession最终被关闭
      spark.stop()
    }
  }

  // 提取表处理逻辑为单独的方法
  def processTable(spark: SparkSession, tableName: String, jdbcUrl: String, username: String, 
                   password: String, driver: String, isDaily: Boolean): Unit = {
    try {
      println(s"正在处理表: $tableName")

      // 读取表数据
      val df = spark.read
        .format("jdbc")
        .option("url", jdbcUrl)
        .option("dbtable", tableName)
        .option("user", username)
        .option("password", password)
        .option("driver", driver)
        .load()

      // 根据表类型确定所需字段
      val requiredColumns = if (isDaily) {
        List("stock_code", "stock_name", "trade_date", "close_price", "volume", "change_amount")
      } else {
        List("stock_code", "stock_name", "trade_datetime", "close_price", "volume", "change_amount")
      }

      // 检查是否存在所需的字段
      val missingColumns = requiredColumns.filter(!df.columns.contains(_))

      if (missingColumns.isEmpty) {
        // 选择所需字段
        val processedDF = df.select(requiredColumns.map(col): _*)

        // 写入新表
        val newTableName = tableName + "_new"
        processedDF.write
          .format("jdbc")
          .option("url", jdbcUrl)
          .option("dbtable", newTableName)
          .option("user", username)
          .option("password", password)
          .option("driver", driver)
          .mode("overwrite")
          .save()

        println(s"成功创建新表: $newTableName")
      } else {
        println(s"跳过表 $tableName，缺少必要字段: ${missingColumns.mkString(", ")}")
      }
    } catch {
      case e: Exception =>
        println(s"处理表 $tableName 时出错: ${e.getMessage}")
        e.printStackTrace()
    }
  }
}    