"""
项目配置文件
包含爬虫、数据库、API等相关配置
"""

import os
import logging
from datetime import datetime

class Config:
    """基础配置类"""
    
    # 项目基础信息
    PROJECT_NAME = "农业上市公司行情监控平台"
    VERSION = "1.0.0"
    
    # 文件路径配置
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    LOG_DIR = os.path.join(BASE_DIR, "logs")
    MODELS_DIR = os.path.join(BASE_DIR, "models")

    # 确保目录存在
    os.makedirs(LOG_DIR, exist_ok=True)
    os.makedirs(MODELS_DIR, exist_ok=True)

class CrawlerConfig:
    """爬虫相关配置"""
    
    # 请求配置
    REQUEST_TIMEOUT = 15  # 请求超时时间（秒）
    RETRY_TIMES = 3       # 重试次数
    RETRY_DELAY = 2       # 重试延迟（秒）
    REQUEST_INTERVAL = 1  # 请求间隔（秒）
    BATCH_SIZE = 20       # 批量处理大小

    # API接口配置
    STOCK_API_URL = "http://push2.eastmoney.com/api/qt/ulist.np/get"  # 股票数据API
    CONCEPT_API_URL = "http://push2.eastmoney.com/api/qt/clist/get"   # 概念股API
    HISTORICAL_API_URL = "http://push2his.eastmoney.com/api/qt/stock/kline/get"  # 历史数据API
    REALTIME_API_URL = "http://push2.eastmoney.com/api/qt/stock/get"  # 实时数据API
    
    # User-Agent配置
    USE_RANDOM_UA = True  # 是否使用随机User-Agent
    DEFAULT_UA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    
    # 请求头配置
    DEFAULT_HEADERS = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Referer': 'https://www.baidu.com/',
    }

    # 数据保存配置
    SAVE_CONFIG = {
        'csv_encoding': 'utf-8-sig',   # CSV文件编码
        'include_timestamp': True       # 文件名是否包含时间戳
    }
    
    # 文件命名模板
    FILE_TEMPLATES = {
        'concept_stocks': 'agricultural_stocks_{timestamp}.csv',
        'historical_data': 'historical_{stock_code}_{days}days_{timestamp}.csv'
    }

    # 历史数据配置
    HISTORICAL_CONFIG = {
        'default_days': 60,           # 默认获取天数
        'max_days': 365,              # 最大获取天数
        'kline_type': '101',          # K线类型：101=日K，102=周K，103=月K
        'fq_type': '1',               # 复权类型：0=不复权，1=前复权，2=后复权
        'fields1': 'f1,f2,f3,f4,f5,f6',  # 基础字段
        'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',  # K线字段
        'ut_token': 'fa5fd1943c7b386f172d6893dbfba10b'  # 请求令牌
    }

    # 实时数据配置
    REALTIME_CONFIG = {
        'update_interval': 60,  # 实时数据更新间隔（秒）
        'fields': 'f43,f44,f45,f46,f47,f48,f49,f50,f51,f52,f53,f54,f55,f56,f57,f58',  # 实时数据字段
        'ut_token': 'bd1d9ddb04089700cf9c27f6f7426281'
    }

    # 知名农业公司股票代码配置
    AGRICULTURAL_STOCKS = {
        # 养殖业
        '000876': {'name': '新希望', 'category': '饲料养殖', 'industry': '养殖业'},
        '300498': {'name': '温氏股份', 'category': '生猪养殖', 'industry': '养殖业'},
        '002714': {'name': '牧原股份', 'category': '生猪养殖', 'industry': '养殖业'},
        '002157': {'name': '正邦科技', 'category': '生猪养殖', 'industry': '养殖业'},
        '002124': {'name': '天邦股份', 'category': '生猪养殖', 'industry': '养殖业'},
        '002567': {'name': '唐人神', 'category': '生猪养殖', 'industry': '养殖业'},
        '002385': {'name': '大北农', 'category': '饲料种业', 'industry': '养殖业'},
        '002311': {'name': '海大集团', 'category': '饲料水产', 'industry': '养殖业'},
        '300106': {'name': '西部牧业', 'category': '乳业养殖', 'industry': '养殖业'},

        # 种业
        '000998': {'name': '隆平高科', 'category': '种业', 'industry': '种业'},
        '002041': {'name': '登海种业', 'category': '种业', 'industry': '种业'},
        '000713': {'name': '丰乐种业', 'category': '种业', 'industry': '种业'},
        '300189': {'name': '神农科技', 'category': '种业', 'industry': '种业'},
        '600313': {'name': '农发种业', 'category': '种业', 'industry': '种业'},
        '300087': {'name': '荃银高科', 'category': '种业', 'industry': '种业'},

        # 农产品加工
        '000895': {'name': '双汇发展', 'category': '肉制品', 'industry': '食品加工'},
        '002515': {'name': '金字火腿', 'category': '肉制品', 'industry': '食品加工'},
        '002330': {'name': '得利斯', 'category': '肉制品', 'industry': '食品加工'},
        '002840': {'name': '华统股份', 'category': '肉制品', 'industry': '食品加工'},
        '002726': {'name': '龙大肉食', 'category': '肉制品', 'industry': '食品加工'},

        # 乳业
        '600887': {'name': '伊利股份', 'category': '乳制品', 'industry': '乳业'},
        '600597': {'name': '光明乳业', 'category': '乳制品', 'industry': '乳业'},
        '600429': {'name': '三元股份', 'category': '乳制品', 'industry': '乳业'},
        '002570': {'name': '贝因美', 'category': '乳制品', 'industry': '乳业'},

        # 水产
        '002069': {'name': '獐子岛', 'category': '水产品', 'industry': '水产业'},
        '300094': {'name': '国联水产', 'category': '水产品', 'industry': '水产业'},
        '600257': {'name': '大湖股份', 'category': '水产品', 'industry': '水产业'},
        '300138': {'name': '晨光生物', 'category': '植物提取', 'industry': '生物技术'},

        # 农资
        '000902': {'name': '新洋丰', 'category': '化肥', 'industry': '农资'},
        '002470': {'name': '金正大', 'category': '化肥', 'industry': '农资'},
        '002588': {'name': '史丹利', 'category': '化肥', 'industry': '农资'},
        '002170': {'name': '芭田股份', 'category': '化肥', 'industry': '农资'},
        '600486': {'name': '扬农化工', 'category': '农药', 'industry': '农资'},
        '000553': {'name': '沙隆达A', 'category': '农药', 'industry': '农资'},

        # 农垦
        '600598': {'name': '北大荒', 'category': '农垦', 'industry': '农垦'},
        '601952': {'name': '苏垦农发', 'category': '农垦', 'industry': '农垦'},
        '600359': {'name': '新农开发', 'category': '农垦', 'industry': '农垦'},

        # 食品饮料（农业相关）
        '000858': {'name': '五粮液', 'category': '白酒', 'industry': '酒业'},
        '600519': {'name': '贵州茅台', 'category': '白酒', 'industry': '酒业'},
        '000568': {'name': '泸州老窖', 'category': '白酒', 'industry': '酒业'},
        '600779': {'name': '水井坊', 'category': '白酒', 'industry': '酒业'},
        '000596': {'name': '古井贡酒', 'category': '白酒', 'industry': '酒业'},
        '600809': {'name': '山西汾酒', 'category': '白酒', 'industry': '酒业'},
        '000729': {'name': '燕京啤酒', 'category': '啤酒', 'industry': '酒业'},
        '600600': {'name': '青岛啤酒', 'category': '啤酒', 'industry': '酒业'},
        '002304': {'name': '洋河股份', 'category': '白酒', 'industry': '酒业'},
    }

class DatabaseConfig:
    """数据库配置"""

    # MySQL配置
    MYSQL_CONFIG = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'q20040619',
        'database': 'agricultural_data',
        'charset': 'utf8mb4',
        'autocommit': True
    }

    # 数据表配置
    TABLE_CONFIG = {
        'table_name': 'total_data',
        'auto_create': True,
        'drop_if_exists': False  # 不删除表，使用更新模式
    }

    # 实时数据表配置
    REALTIME_TABLE_CONFIG = {
        'table_prefix': 'realtime_',  # 实时数据表前缀
        'auto_create': True,
        'retention_hours': 24  # 数据保留时间（小时）
    }

class PredictionConfig:
    """预测模型配置"""

    # 模型基础配置
    MODEL_CONFIG = {
        'lstm_units': 50,
        'epochs': 100,
        'batch_size': 64,
        'learning_rate': 0.001,
        'dropout_rate': 0.2
    }

    # 1小时内预测配置
    HOURLY_PREDICTION = {
        'sequence_length': 60,  # 使用60分钟数据
        'prediction_minutes': 60,  # 预测未来60分钟
        'features': ['close_price', 'volume', 'change_percent'],  # 预测特征
        'model_name': 'hourly_lstm_model',
        'update_interval': 300  # 模型更新间隔（秒）
    }

    # 3天预测配置
    DAILY_PREDICTION = {
        'sequence_length': 30,  # 使用30天数据
        'prediction_days': 3,   # 预测未来3天
        'features': ['close_price', 'volume', 'change_percent'],  # 预测特征
        'model_name': 'daily_lstm_model',
        'retrain_interval': 86400  # 模型重训练间隔（秒，1天）
    }

    # 模型文件路径
    MODEL_PATHS = {
        'models_dir': Config.MODELS_DIR,
        'hourly_model_template': 'hourly_lstm_{stock_code}.h5',
        'daily_model_template': 'daily_lstm_{stock_code}.h5',
        'scaler_template': 'scaler_{model_type}_{stock_code}.pkl'
    }

class LogConfig:
    """日志配置"""
    
    # 日志级别
    LOG_LEVEL = 'INFO'
    
    # 日志格式
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
    
    # 日志文件配置
    LOG_FILES = {
        'crawler': os.path.join(Config.LOG_DIR, 'crawler.log'),
        'database': os.path.join(Config.LOG_DIR, 'database.log'),
        'analysis': os.path.join(Config.LOG_DIR, 'analysis.log')
    }
    
    # 日志轮转配置
    LOG_ROTATION = {
        'max_bytes': 10 * 1024 * 1024,  # 10MB
        'backup_count': 5,
        'encoding': 'utf-8'
    }

def get_timestamp():
    """获取当前时间戳字符串"""
    return datetime.now().strftime('%Y%m%d_%H%M%S')

def get_date():
    """获取当前日期字符串"""
    return datetime.now().strftime('%Y%m%d')

def get_config_summary():
    """获取配置摘要信息"""
    return {
        'project_name': Config.PROJECT_NAME,
        'version': Config.VERSION,
        'log_level': LogConfig.LOG_LEVEL,
        'log_dir': Config.LOG_DIR,
        'models_dir': Config.MODELS_DIR
    }

def setup_error_logger():
    """设置统一的错误日志记录器"""
    # 创建logs目录
    log_dir = os.path.join(Config.BASE_DIR, 'logs')
    os.makedirs(log_dir, exist_ok=True)

    # 配置错误日志记录器
    error_logger = logging.getLogger('total_error')
    error_logger.setLevel(logging.ERROR)

    # 清除已有的处理器
    error_logger.handlers.clear()

    # 文件处理器 - 记录到total_error.log
    error_log_file = os.path.join(log_dir, 'total_error.log')
    file_handler = logging.FileHandler(error_log_file, encoding='utf-8')
    file_handler.setLevel(logging.ERROR)

    # 错误日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s')
    file_handler.setFormatter(formatter)

    error_logger.addHandler(file_handler)

    return error_logger

def log_error(error_message, module_name="Unknown", exception=None):
    """统一的错误日志记录函数"""
    error_logger = setup_error_logger()

    if exception:
        error_logger.error(f"[{module_name}] {error_message} - Exception: {str(exception)}")
    else:
        error_logger.error(f"[{module_name}] {error_message}")

# 导出主要配置类
__all__ = [
    'Config',
    'CrawlerConfig',
    'DatabaseConfig',
    'PredictionConfig',
    'LogConfig',
    'get_timestamp',
    'get_date',
    'get_config_summary',
    'setup_error_logger',
    'log_error'
]
