#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
农业股票监控平台 - 数据大屏API接口
提供随机股票数据、总市值数据等API服务
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from flask import Flask, jsonify
from flask_cors import CORS
import random
import pandas as pd
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text
import logging

# 导入配置
from config import DatabaseConfig, CrawlerConfig

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

class DashboardAPI:
    def __init__(self):
        self.db_config = DatabaseConfig()
        self.crawler_config = CrawlerConfig()
        self.db_engine = None
        self._init_database()
    
    def _init_database(self):
        """初始化数据库连接"""
        try:
            mysql_config = self.db_config.MYSQL_CONFIG
            connection_string = (
                f"mysql+pymysql://{mysql_config['user']}:{mysql_config['password']}"
                f"@{mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}"
                f"?charset={mysql_config['charset']}"
            )
            self.db_engine = create_engine(connection_string, echo=False)
            
            # 测试连接
            with self.db_engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            logger.info("数据大屏API数据库连接初始化成功")
        except Exception as e:
            logger.error(f"数据大屏API数据库连接失败: {e}")
            self.db_engine = None
    
    def get_random_stock(self):
        """获取随机股票代码"""
        return random.choice(list(self.crawler_config.AGRICULTURAL_STOCKS.keys()))
    
    def get_stock_90d_data(self, stock_code, data_type='volume'):
        """获取股票90天数据（日级）"""
        if not self.db_engine:
            return None
        
        try:
            table_name = f"{stock_code}_daily"
            
            # 计算90天前的日期
            end_date = datetime.now()
            start_date = end_date - timedelta(days=90)
            
            # 查询数据
            query = f"""
            SELECT trade_date, {data_type}, stock_name
            FROM {table_name}
            WHERE trade_date >= %(start_date)s AND trade_date <= %(end_date)s
            ORDER BY trade_date ASC
            LIMIT 90
            """
            
            with self.db_engine.connect() as conn:
                df = pd.read_sql(query, conn, params={
                    'start_date': start_date.date(),
                    'end_date': end_date.date()
                })
            
            if df.empty:
                return None
            
            # 确保trade_date是datetime类型
            df['trade_date'] = pd.to_datetime(df['trade_date'])

            # 格式化数据
            result = {
                'stock_code': stock_code,
                'stock_name': df.iloc[0]['stock_name'],
                'dates': df['trade_date'].dt.strftime('%Y-%m-%d').tolist(),
                'values': df[data_type].fillna(0).tolist(),
                'data_type': data_type
            }
            
            return result
            
        except Exception as e:
            logger.error(f"获取{stock_code}的90天{data_type}数据失败: {e}")
            return None
    
    def get_stock_7d_data(self, stock_code, data_type='volume'):
        """获取股票7天数据（小时级，基于分钟数据聚合）"""
        if not self.db_engine:
            return None
        
        try:
            table_name = f"{stock_code}_minute"
            
            # 计算7天前的日期
            end_datetime = datetime.now()
            start_datetime = end_datetime - timedelta(days=7)
            
            # 查询并按小时聚合数据
            query = f"""
            SELECT
                DATE_FORMAT(trade_datetime, '%%Y-%%m-%%d %%H:00:00') as hour_time,
                SUM({data_type}) as {data_type},
                stock_name
            FROM {table_name}
            WHERE trade_datetime >= %(start_datetime)s AND trade_datetime <= %(end_datetime)s
            GROUP BY DATE_FORMAT(trade_datetime, '%%Y-%%m-%%d %%H:00:00'), stock_name
            ORDER BY hour_time ASC
            """

            with self.db_engine.connect() as conn:
                df = pd.read_sql(query, conn, params={
                    'start_datetime': start_datetime,
                    'end_datetime': end_datetime
                })
            
            if df.empty:
                return None
            
            # 确保hour_time格式正确
            df['hour_time'] = pd.to_datetime(df['hour_time'])

            # 格式化数据
            result = {
                'stock_code': stock_code,
                'stock_name': df.iloc[0]['stock_name'],
                'hours': df['hour_time'].dt.strftime('%Y-%m-%d %H:%M:%S').tolist(),
                'values': df[data_type].fillna(0).tolist(),
                'data_type': data_type
            }
            
            return result
            
        except Exception as e:
            logger.error(f"获取{stock_code}的7天{data_type}数据失败: {e}")
            return None
    
    def get_market_cap_data(self):
        """获取总市值数据"""
        if not self.db_engine:
            return None
        
        try:
            table_name = self.db_config.TABLE_CONFIG['table_name']
            
            query = f"""
            SELECT stock_code, stock_name, total_market_value
            FROM {table_name}
            WHERE total_market_value IS NOT NULL AND total_market_value > 0
            ORDER BY total_market_value DESC
            """
            
            with self.db_engine.connect() as conn:
                df = pd.read_sql(query, conn)
            
            if df.empty:
                return None
            
            # 格式化数据
            result = []
            for _, row in df.iterrows():
                result.append({
                    'name': f"{row['stock_name']}({row['stock_code']})",
                    'value': float(row['total_market_value']),
                    'stock_code': row['stock_code']
                })
            
            return result
            
        except Exception as e:
            logger.error(f"获取总市值数据失败: {e}")
            return None

# 创建API实例
dashboard_api = DashboardAPI()

@app.route('/api/random-stock-90d/<data_type>')
def get_random_stock_90d(data_type):
    """获取随机股票90天数据"""
    try:
        stock_code = dashboard_api.get_random_stock()
        data = dashboard_api.get_stock_90d_data(stock_code, data_type)
        
        if data:
            return jsonify({
                'success': True,
                'data': data
            })
        else:
            return jsonify({
                'success': False,
                'message': '数据获取失败'
            }), 500
            
    except Exception as e:
        logger.error(f"随机股票90天数据API错误: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@app.route('/api/random-stock-7d/<data_type>')
def get_random_stock_7d(data_type):
    """获取随机股票7天数据"""
    try:
        stock_code = dashboard_api.get_random_stock()
        data = dashboard_api.get_stock_7d_data(stock_code, data_type)
        
        if data:
            return jsonify({
                'success': True,
                'data': data
            })
        else:
            return jsonify({
                'success': False,
                'message': '数据获取失败'
            }), 500
            
    except Exception as e:
        logger.error(f"随机股票7天数据API错误: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@app.route('/api/market-cap')
def get_market_cap():
    """获取总市值数据"""
    try:
        data = dashboard_api.get_market_cap_data()
        
        if data:
            return jsonify({
                'success': True,
                'data': data
            })
        else:
            return jsonify({
                'success': False,
                'message': '数据获取失败'
            }), 500
            
    except Exception as e:
        logger.error(f"总市值数据API错误: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@app.route('/api/health')
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("启动农业股票监控平台API服务器...")
    print("API服务器地址: http://localhost:5000")
    print("数据库连接: MySQL agricultural_data")
    print("按 Ctrl+C 停止服务器")
    app.run(host='0.0.0.0', port=5000, debug=True)
