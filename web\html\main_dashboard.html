<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>农业股票监控平台 - 数据大屏</title>
    <link rel="stylesheet" href="../css/main.css">
    <style>
        body {
            background-image: url('../image/1.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            background-repeat: no-repeat;
        }
        body::before {
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(96, 165, 250, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(147, 197, 253, 0.1) 0%, transparent 50%);
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="logo">
                <div class="logo-icon">🌾</div>
                <div class="logo-text">农业股票监控平台</div>
            </div>
            <nav class="nav-buttons">
                <a href="#" class="nav-btn active" data-page="dashboard">数据大屏</a>
                <a href="prediction.html" class="nav-btn" data-page="prediction">未来预测</a>
                <a href="#" class="nav-btn" data-page="ranking">今日排行榜</a>
                <a href="#" class="nav-btn" data-page="realtime">实时监测</a>
            </nav>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 左侧面板 - 90天数据 -->
            <div class="left-panel">
                <!-- 90天成交量折线图 -->
                <div class="data-window volume-90d">
                    <div class="window-title" id="volume-90d-title">
                        <span class="stock-info">加载中...</span> - 近90天成交量趋势
                    </div>
                    <div class="chart-container">
                        <div id="volume-90d-chart" style="width: 100%; height: 100%;"></div>
                    </div>
                </div>

                <!-- 90天成交额折线图 -->
                <div class="data-window turnover-90d">
                    <div class="window-title" id="turnover-90d-title">
                        <span class="stock-info">加载中...</span> - 近90天成交额趋势
                    </div>
                    <div class="chart-container">
                        <div id="turnover-90d-chart" style="width: 100%; height: 100%;"></div>
                    </div>
                </div>
            </div>

            <!-- 中央面板 - 总市值扇形图 -->
            <div class="center-panel">
                <div class="data-window market-cap-pie">
                    <div class="window-title">
                        46只农业股票总市值分布
                    </div>
                    <div class="chart-container">
                        <div id="market-cap-pie-chart" style="width: 100%; height: 100%;"></div>
                    </div>
                </div>
            </div>

            <!-- 右侧面板 - 24小时数据 -->
            <div class="right-panel">
                <!-- 24小时成交量折线图 -->
                <div class="data-window volume-7d">
                    <div class="window-title" id="volume-7d-title">
                        <span class="stock-info">加载中...</span> - 近24小时成交量趋势
                    </div>
                    <div class="chart-container">
                        <div id="volume-7d-chart" style="width: 100%; height: 100%;"></div>
                    </div>
                </div>

                <!-- 24小时成交额折线图 -->
                <div class="data-window turnover-7d">
                    <div class="window-title" id="turnover-7d-title">
                        <span class="stock-info">加载中...</span> - 近24小时成交额趋势
                    </div>
                    <div class="chart-container">
                        <div id="turnover-7d-chart" style="width: 100%; height: 100%;"></div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="../js/dashboard.js"></script>
    <script src="../js/charts.js"></script>
    <script src="../js/api.js"></script>
</body>
</html>
