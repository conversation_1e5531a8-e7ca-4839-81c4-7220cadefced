/**
 * 农业股票监控平台 - 仪表板主逻辑
 * 负责页面初始化、数据刷新和用户交互
 */

class Dashboard {
    constructor() {
        this.refreshInterval = 15000; // 15秒刷新间隔
        this.timers = {};
        this.isInitialized = false;
    }

    /**
     * 初始化仪表板
     */
    async init() {
        try {
            console.log('初始化农业股票监控平台...');
            
            // 初始化图表
            this.initCharts();
            
            // 加载初始数据
            await this.loadAllData();
            
            // 设置自动刷新
            this.setupAutoRefresh();
            
            // 设置导航事件
            this.setupNavigation();
            
            this.isInitialized = true;
            console.log('仪表板初始化完成');
            
        } catch (error) {
            console.error('仪表板初始化失败:', error);
            this.showError('系统初始化失败，请刷新页面重试');
        }
    }

    /**
     * 初始化所有图表
     */
    initCharts() {
        // 初始化90天成交量图表
        chartManager.initChart('volume-90d-chart', 'lineChart');
        
        // 初始化90天成交额图表
        chartManager.initChart('turnover-90d-chart', 'lineChart');
        
        // 初始化7天成交量图表
        chartManager.initChart('volume-7d-chart', 'lineChart');
        
        // 初始化7天成交额图表
        chartManager.initChart('turnover-7d-chart', 'lineChart');
        
        // 初始化总市值扇形图
        chartManager.initChart('market-cap-pie-chart', 'pieChart');
        
        console.log('所有图表初始化完成');
    }

    /**
     * 加载所有数据
     */
    async loadAllData() {
        const loadPromises = [
            this.loadVolume90dData(),
            this.loadTurnover90dData(),
            this.loadVolume7dData(),
            this.loadTurnover7dData(),
            this.loadMarketCapData()
        ];

        // 并行加载所有数据
        await Promise.allSettled(loadPromises);
    }

    /**
     * 加载90天成交量数据
     */
    async loadVolume90dData() {
        try {
            chartManager.showLoading('volume-90d-chart');
            const data = await dashboardAPI.getRandomStock90dVolume();
            
            if (data) {
                chartManager.updateLineChart('volume-90d-chart', data);
                this.updateTitle('volume-90d-title', data.stock_name, data.stock_code, '近90天成交量趋势');
            }
        } catch (error) {
            console.error('加载90天成交量数据失败:', error);
        } finally {
            chartManager.hideLoading('volume-90d-chart');
        }
    }

    /**
     * 加载90天成交额数据
     */
    async loadTurnover90dData() {
        try {
            chartManager.showLoading('turnover-90d-chart');
            const data = await dashboardAPI.getRandomStock90dTurnover();
            
            if (data) {
                chartManager.updateLineChart('turnover-90d-chart', data);
                this.updateTitle('turnover-90d-title', data.stock_name, data.stock_code, '近90天成交额趋势');
            }
        } catch (error) {
            console.error('加载90天成交额数据失败:', error);
        } finally {
            chartManager.hideLoading('turnover-90d-chart');
        }
    }

    /**
     * 加载7天成交量数据
     */
    async loadVolume7dData() {
        try {
            chartManager.showLoading('volume-7d-chart');
            const data = await dashboardAPI.getRandomStock7dVolume();

            if (data) {
                chartManager.updateLineChart('volume-7d-chart', data);
                this.updateTitle('volume-7d-title', data.stock_name, data.stock_code, '近24小时成交量趋势');
            }
        } catch (error) {
            console.error('加载7天成交量数据失败:', error);
        } finally {
            chartManager.hideLoading('volume-7d-chart');
        }
    }

    /**
     * 加载7天成交额数据
     */
    async loadTurnover7dData() {
        try {
            chartManager.showLoading('turnover-7d-chart');
            const data = await dashboardAPI.getRandomStock7dTurnover();

            if (data) {
                chartManager.updateLineChart('turnover-7d-chart', data);
                this.updateTitle('turnover-7d-title', data.stock_name, data.stock_code, '近24小时成交额趋势');
            }
        } catch (error) {
            console.error('加载7天成交额数据失败:', error);
        } finally {
            chartManager.hideLoading('turnover-7d-chart');
        }
    }

    /**
     * 加载总市值数据
     */
    async loadMarketCapData() {
        try {
            chartManager.showLoading('market-cap-pie-chart');
            const data = await dashboardAPI.getMarketCapData();
            
            if (data && data.length > 0) {
                chartManager.updatePieChart('market-cap-pie-chart', data);
            }
        } catch (error) {
            console.error('加载总市值数据失败:', error);
        } finally {
            chartManager.hideLoading('market-cap-pie-chart');
        }
    }

    /**
     * 更新窗口标题
     * @param {string} titleId - 标题元素ID
     * @param {string} stockName - 股票名称
     * @param {string} stockCode - 股票代码
     * @param {string} suffix - 标题后缀
     */
    updateTitle(titleId, stockName, stockCode, suffix) {
        const titleElement = document.getElementById(titleId);
        if (titleElement) {
            const stockInfo = titleElement.querySelector('.stock-info');
            if (stockInfo) {
                stockInfo.textContent = `${stockName}(${stockCode})`;
            }
        }
    }

    /**
     * 设置自动刷新
     */
    setupAutoRefresh() {
        // 清除现有定时器
        this.clearAllTimers();

        // 设置随机股票数据刷新（每15秒）
        this.timers.randomData = setInterval(() => {
            this.loadVolume90dData();
            this.loadTurnover90dData();
            this.loadVolume7dData();
            this.loadTurnover7dData();
        }, this.refreshInterval);

        // 设置总市值数据刷新（每60秒）
        this.timers.marketCap = setInterval(() => {
            this.loadMarketCapData();
        }, 60000);

        console.log('自动刷新已设置');
    }

    /**
     * 设置导航事件
     */
    setupNavigation() {
        const navButtons = document.querySelectorAll('.nav-btn');
        
        navButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                
                const page = button.getAttribute('data-page');
                
                // 更新按钮状态
                navButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                
                // 处理页面切换
                this.handlePageSwitch(page);
            });
        });
    }

    /**
     * 处理页面切换
     * @param {string} page - 页面名称
     */
    handlePageSwitch(page) {
        switch (page) {
            case 'dashboard':
                // 当前就是数据大屏，无需切换
                break;
            case 'prediction':
                // 跳转到预测页面
                window.location.href = 'prediction.html';
                break;
            case 'ranking':
                // 跳转到排行榜页面
                window.location.href = 'ranking.html';
                break;
            case 'realtime':
                // 跳转到实时监测页面
                window.location.href = 'realtime_monitor.html';
                break;
            default:
                console.warn('未知页面:', page);
        }
    }

    /**
     * 显示错误信息
     * @param {string} message - 错误信息
     */
    showError(message) {
        // 这里可以实现更复杂的错误提示UI
        console.error(message);
        alert(message);
    }

    /**
     * 清除所有定时器
     */
    clearAllTimers() {
        Object.values(this.timers).forEach(timer => {
            if (timer) clearInterval(timer);
        });
        this.timers = {};
    }

    /**
     * 销毁仪表板
     */
    destroy() {
        this.clearAllTimers();
        chartManager.destroyAllCharts();
        this.isInitialized = false;
        console.log('仪表板已销毁');
    }
}

// 创建全局仪表板实例
window.dashboard = new Dashboard();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    dashboard.init();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    dashboard.destroy();
});
