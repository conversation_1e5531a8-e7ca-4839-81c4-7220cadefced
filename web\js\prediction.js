/**
 * 预测界面JavaScript
 */

class PredictionManager {
    constructor() {
        this.chart = null;
        this.stocksData = null;
        this.init();
    }

    /**
     * 初始化
     */
    init() {
        this.bindEvents();
        this.loadStocks();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        const stockSelect = document.getElementById('stock-select');
        const predictBtn = document.getElementById('predict-btn');

        // 股票选择变化
        stockSelect.addEventListener('change', () => {
            predictBtn.disabled = !stockSelect.value;
        });

        // 预测按钮点击
        predictBtn.addEventListener('click', () => {
            this.startPrediction();
        });
    }

    /**
     * 加载股票列表
     */
    async loadStocks() {
        try {
            const response = await fetch('http://localhost:5000/api/stocks');
            const data = await response.json();

            if (data.success) {
                this.stocksData = data.data;
                this.populateStockSelect(data.data);
            } else {
                console.error('加载股票列表失败:', data.error);
                this.showError('加载股票列表失败: ' + data.error);
            }
        } catch (error) {
            console.error('加载股票列表失败:', error);
            this.showError('无法连接到服务器，请确保服务器正在运行');
        }
    }

    /**
     * 填充股票选择框
     */
    populateStockSelect(stocks) {
        const stockSelect = document.getElementById('stock-select');
        
        // 清空现有选项
        stockSelect.innerHTML = '<option value="">请选择股票...</option>';

        // 按分类分组
        const categories = {};
        stocks.forEach(stock => {
            if (!categories[stock.category]) {
                categories[stock.category] = [];
            }
            categories[stock.category].push(stock);
        });

        // 添加分组选项
        Object.keys(categories).forEach(category => {
            const optgroup = document.createElement('optgroup');
            optgroup.label = category;

            categories[category].forEach(stock => {
                const option = document.createElement('option');
                option.value = stock.code;
                option.textContent = `${stock.name} (${stock.code})`;
                optgroup.appendChild(option);
            });

            stockSelect.appendChild(optgroup);
        });
    }

    /**
     * 开始预测
     */
    async startPrediction() {
        const stockCode = document.getElementById('stock-select').value;
        const predictionType = document.getElementById('prediction-type').value;

        if (!stockCode) {
            alert('请选择股票');
            return;
        }

        // 显示加载状态
        this.showLoading();

        try {
            const response = await fetch('http://localhost:5000/api/predict', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    stock_code: stockCode,
                    prediction_type: predictionType
                })
            });

            const data = await response.json();

            if (data.success) {
                this.displayPredictionResult(data);
            } else {
                this.hideLoading();
                this.showError('预测失败: ' + data.error);
            }
        } catch (error) {
            console.error('预测失败:', error);
            this.hideLoading();
            this.showError('预测失败: ' + error.message);
        }
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        document.getElementById('chart-loading').style.display = 'flex';
        document.getElementById('chart-placeholder').style.display = 'none';
        document.getElementById('analysis-placeholder').style.display = 'flex';
        document.getElementById('analysis-result').style.display = 'none';
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        document.getElementById('chart-loading').style.display = 'none';
        document.getElementById('chart-placeholder').style.display = 'none';
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        alert(message);
        document.getElementById('chart-placeholder').style.display = 'flex';
        document.getElementById('analysis-placeholder').style.display = 'flex';
        document.getElementById('analysis-result').style.display = 'none';
    }

    /**
     * 显示预测结果
     */
    displayPredictionResult(data) {
        this.hideLoading();

        // 更新图表标题
        const predictionType = data.prediction_type;
        const title = predictionType === '3day' ? '[PREDICTION] 3-Day Comprehensive Data Forecast' : '[PREDICTION] 1-Hour Comprehensive Data Forecast';
        document.getElementById('chart-title').textContent = title;

        // 更新图表
        this.updateChart(data);

        // 更新分析结果
        this.updateAnalysis(data);
    }

    /**
     * 更新图表
     */
    updateChart(data) {
        const ctx = document.getElementById('prediction-chart').getContext('2d');

        if (this.chart) {
            this.chart.destroy();
        }

        const historicalData = data.historical_data;
        const predictionData = data.prediction_data;

        // 准备图表数据
        const labels = [...historicalData.datetime, ...predictionData.datetime].map(time => {
            const date = new Date(time);
            return data.prediction_type === '3day'
                ? date.toLocaleDateString()
                : date.toLocaleDateString() + ' ' + date.toLocaleTimeString().slice(0, 5);
        });

        // 历史数据（实线）
        const historicalPrices = [...historicalData.close_price, ...new Array(predictionData.close_price.length).fill(null)];
        const historicalVolume = [...historicalData.volume, ...new Array(predictionData.volume.length).fill(null)];
        const historicalChange = [...historicalData.change_percent, ...new Array(predictionData.change_percent.length).fill(null)];

        // 预测数据（虚线）
        const predictedPrices = [...new Array(historicalData.close_price.length).fill(null), ...predictionData.close_price];
        const predictedVolume = [...new Array(historicalData.volume.length).fill(null), ...predictionData.volume];
        const predictedChange = [...new Array(historicalData.change_percent.length).fill(null), ...predictionData.change_percent];

        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    // 成交量 - 历史数据（柱状图）
                    {
                        label: '历史成交量',
                        type: 'bar',
                        data: historicalVolume,
                        backgroundColor: 'rgba(6, 182, 212, 0.6)',
                        borderColor: '#06b6d4',
                        borderWidth: 1,
                        yAxisID: 'y-volume',
                        order: 3
                    },
                    // 成交量 - 预测数据（柱状图）
                    {
                        label: '预测成交量',
                        type: 'bar',
                        data: predictedVolume,
                        backgroundColor: 'rgba(30, 64, 175, 0.8)',
                        borderColor: '#1e40af',
                        borderWidth: 2,
                        borderDash: [3, 3],
                        yAxisID: 'y-volume',
                        order: 3
                    },
                    // 收盘价 - 历史数据
                    {
                        label: '历史收盘价',
                        type: 'line',
                        data: historicalPrices,
                        borderColor: '#4ade80',
                        backgroundColor: 'rgba(74, 222, 128, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1,
                        pointRadius: 1,
                        yAxisID: 'y-price',
                        order: 1
                    },
                    // 收盘价 - 预测数据（绿色虚线）
                    {
                        label: '预测收盘价',
                        type: 'line',
                        data: predictedPrices,
                        borderColor: '#22c55e',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        borderWidth: 3,
                        borderDash: [8, 4],
                        fill: false,
                        tension: 0.1,
                        pointRadius: 2,
                        pointBackgroundColor: '#22c55e',
                        yAxisID: 'y-price',
                        order: 1
                    },
                    // 涨跌幅 - 历史数据
                    {
                        label: '历史涨跌幅',
                        type: 'line',
                        data: historicalChange,
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1,
                        pointRadius: 1,
                        yAxisID: 'y-change',
                        order: 2
                    },
                    // 涨跌幅 - 预测数据
                    {
                        label: '预测涨跌幅',
                        type: 'line',
                        data: predictedChange,
                        borderColor: '#dc2626',
                        backgroundColor: 'rgba(220, 38, 38, 0.1)',
                        borderWidth: 3,
                        borderDash: [5, 5],
                        fill: false,
                        tension: 0.1,
                        pointRadius: 2,
                        pointBackgroundColor: '#dc2626',
                        yAxisID: 'y-change',
                        order: 2
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            color: '#94a3b8',
                            font: {
                                size: 12
                            },
                            usePointStyle: true,
                            pointStyle: 'line'
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                const value = context.parsed.y;
                                if (value !== null) {
                                    const label = context.dataset.label;
                                    if (label.includes('收盘价')) {
                                        return `${label}: ¥${value.toFixed(2)}`;
                                    } else if (label.includes('成交量')) {
                                        return `${label}: ${(value / 10000).toFixed(2)}万`;
                                    } else if (label.includes('涨跌幅')) {
                                        return `${label}: ${value.toFixed(2)}%`;
                                    }
                                }
                                return null;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#94a3b8',
                            maxTicksLimit: 10
                        },
                        grid: {
                            color: 'rgba(148, 163, 184, 0.1)'
                        }
                    },
                    'y-price': {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        ticks: {
                            color: '#4ade80',
                            callback: function(value) {
                                return '¥' + value.toFixed(2);
                            }
                        },
                        grid: {
                            color: 'rgba(74, 222, 128, 0.1)'
                        },
                        title: {
                            display: true,
                            text: '收盘价 (¥)',
                            color: '#4ade80'
                        }
                    },
                    'y-volume': {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        ticks: {
                            color: '#1e40af',
                            callback: function(value) {
                                return (value / 10000).toFixed(0) + '万';
                            }
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                        title: {
                            display: true,
                            text: '成交量 (万)',
                            color: '#1e40af'
                        }
                    },
                    'y-change': {
                        type: 'linear',
                        display: false,
                        ticks: {
                            color: '#ef4444',
                            callback: function(value) {
                                return value.toFixed(2) + '%';
                            }
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
    }

    /**
     * 更新分析结果
     */
    updateAnalysis(data) {
        const stockInfo = data.stock_info;
        const analysis = data.analysis;

        // 更新股票信息
        document.getElementById('stock-name').textContent = stockInfo.name;
        document.getElementById('stock-code').textContent = stockInfo.code;
        document.getElementById('stock-category').textContent = stockInfo.category;

        // 更新预测摘要
        document.getElementById('trend-value').textContent = analysis.trend;
        document.getElementById('trend-value').className = `summary-value trend-${analysis.trend === '上升' ? 'up' : 'down'}`;

        document.getElementById('price-change-value').textContent = `${analysis.price_change > 0 ? '+' : ''}¥${analysis.price_change.toFixed(2)}`;
        document.getElementById('price-change-value').className = `summary-value trend-${analysis.price_change >= 0 ? 'up' : 'down'}`;

        document.getElementById('change-percent-value').textContent = `${analysis.price_change_percent > 0 ? '+' : ''}${analysis.price_change_percent.toFixed(2)}%`;
        document.getElementById('change-percent-value').className = `summary-value trend-${analysis.price_change_percent >= 0 ? 'up' : 'down'}`;

        document.getElementById('risk-level-value').textContent = analysis.risk_level;
        document.getElementById('risk-level-value').className = `summary-value risk-${analysis.risk_level === '低' ? 'low' : analysis.risk_level === '中' ? 'medium' : 'high'}`;

        // 更新投资建议
        document.getElementById('suggestion-text').textContent = analysis.suggestion;

        // 显示分析结果
        document.getElementById('analysis-placeholder').style.display = 'none';
        document.getElementById('analysis-result').style.display = 'flex';
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new PredictionManager();
});
