#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时监控API服务
处理前端的实时监控请求，启动和管理爬虫进程
"""

import sys
import os
import threading
import time
import pymysql
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from data_collection.realtime_crawler import RealtimeCrawler, log_realtime_info, log_realtime_warning
from config import DatabaseConfig, log_error

class RealtimeMonitorService:
    """实时监控服务"""
    
    def __init__(self):
        self.crawler = RealtimeCrawler()
        self.db_config = DatabaseConfig()
        self.monitoring_threads = {}  # 存储监控线程
        self.monitoring_status = {}   # 存储监控状态
        
    def start_monitoring(self, stock_code):
        """启动股票监控"""
        try:
            # 检查股票代码是否有效
            if stock_code not in self.crawler.agricultural_stocks:
                return {
                    'success': False,
                    'error': f'股票代码 {stock_code} 不在监控列表中'
                }
            
            # 如果已经在监控，先停止
            if stock_code in self.monitoring_threads:
                self.stop_monitoring(stock_code)
            
            # 创建监控表
            start_time = datetime.now()
            table_name = self.crawler.create_realtime_table(stock_code, start_time)
            
            if not table_name:
                return {
                    'success': False,
                    'error': '创建监控表失败'
                }
            
            # 启动监控线程
            monitor_thread = threading.Thread(
                target=self._monitor_worker,
                args=(stock_code, table_name),
                daemon=True
            )
            
            self.monitoring_threads[stock_code] = monitor_thread
            self.monitoring_status[stock_code] = {
                'table_name': table_name,
                'start_time': start_time,
                'is_running': True,
                'data_count': 0
            }
            
            monitor_thread.start()
            
            log_realtime_info(f"启动实时监控: {stock_code}, 表名: {table_name}")
            
            return {
                'success': True,
                'table_name': table_name,
                'message': f'开始监控股票 {stock_code}'
            }
            
        except Exception as e:
            log_error(f"启动监控失败: {e}", "realtime_api.start_monitoring", e)
            return {
                'success': False,
                'error': str(e)
            }
    
    def stop_monitoring(self, stock_code):
        """停止股票监控"""
        try:
            if stock_code in self.monitoring_status:
                self.monitoring_status[stock_code]['is_running'] = False
                
            if stock_code in self.monitoring_threads:
                # 等待线程结束
                thread = self.monitoring_threads[stock_code]
                if thread.is_alive():
                    # 给线程一些时间自然结束
                    time.sleep(1)
                
                del self.monitoring_threads[stock_code]
            
            if stock_code in self.monitoring_status:
                table_name = self.monitoring_status[stock_code]['table_name']
                data_count = self.monitoring_status[stock_code]['data_count']
                log_realtime_info(f"停止监控: {stock_code}, 表名: {table_name}, 共采集 {data_count} 条数据")
                del self.monitoring_status[stock_code]
            
            return {
                'success': True,
                'message': f'停止监控股票 {stock_code}'
            }
            
        except Exception as e:
            log_error(f"停止监控失败: {e}", "realtime_api.stop_monitoring", e)
            return {
                'success': False,
                'error': str(e)
            }
    
    def _monitor_worker(self, stock_code, table_name):
        """监控工作线程"""
        try:
            log_realtime_info(f"监控线程启动: {stock_code}")
            
            while self.monitoring_status.get(stock_code, {}).get('is_running', False):
                # 获取实时数据
                data = self.crawler.get_stock_realtime_data(stock_code)
                
                if data:
                    # 保存数据到数据库
                    if self.crawler.save_realtime_data(table_name, data):
                        # 更新数据计数
                        if stock_code in self.monitoring_status:
                            self.monitoring_status[stock_code]['data_count'] += 1
                            count = self.monitoring_status[stock_code]['data_count']

                        log_realtime_info(f"[{count}] {data['stock_name']} - 价格: ¥{data['current_price']:.2f} "
                                        f"涨跌: {data['change_percent']:+.2f}% - {data['update_time']}")
                    else:
                        log_error("保存实时数据失败", "realtime_api._monitor_worker")
                else:
                    log_realtime_warning(f"获取股票 {stock_code} 实时数据失败")
                
                # 等待5秒
                time.sleep(5)
                
        except Exception as e:
            log_error(f"监控线程异常: {e}", "realtime_api._monitor_worker", e)
        finally:
            log_realtime_info(f"监控线程结束: {stock_code}")
    
    def get_monitoring_data(self, table_name):
        """获取监控数据"""
        try:
            log_realtime_info(f"开始获取监控数据，表名: {table_name}")

            connection = pymysql.connect(**self.db_config.MYSQL_CONFIG)
            cursor = connection.cursor(pymysql.cursors.DictCursor)

            # 首先检查表是否存在
            cursor.execute("""
                SELECT COUNT(*) as table_exists
                FROM information_schema.tables
                WHERE table_schema = %s AND table_name = %s
            """, (self.db_config.MYSQL_CONFIG['database'], table_name))

            table_check = cursor.fetchone()
            if table_check['table_exists'] == 0:
                log_realtime_warning(f"表 {table_name} 不存在")
                return {
                    'success': False,
                    'error': f'表 {table_name} 不存在'
                }

            # 获取最近的数据 - 明确指定前端需要的字段
            query = f"""
            SELECT
                stock_code, stock_name, current_price, change_percent,
                volume, turnover, turnover_rate, pe_ratio, pb_ratio,
                update_time, trade_status
            FROM `{table_name}`
            ORDER BY update_time DESC
            LIMIT 10
            """

            log_realtime_info(f"执行查询: {query}")
            cursor.execute(query)
            results = cursor.fetchall()

            log_realtime_info(f"查询结果数量: {len(results)}")
            if results:
                log_realtime_info(f"最新数据示例: {results[0]}")

            return {
                'success': True,
                'data': results
            }

        except Exception as e:
            log_error(f"获取监控数据失败: {e}", "realtime_api.get_monitoring_data", e)
            return {
                'success': False,
                'error': str(e)
            }
        finally:
            if 'connection' in locals():
                connection.close()
    
    def get_monitoring_status(self):
        """获取所有监控状态"""
        try:
            status_list = []
            for stock_code, status in self.monitoring_status.items():
                stock_info = self.crawler.agricultural_stocks.get(stock_code, {})
                status_list.append({
                    'stock_code': stock_code,
                    'stock_name': stock_info.get('name', '未知'),
                    'table_name': status['table_name'],
                    'start_time': status['start_time'].strftime('%Y-%m-%d %H:%M:%S'),
                    'is_running': status['is_running'],
                    'data_count': status['data_count']
                })
            
            return {
                'success': True,
                'data': status_list
            }
            
        except Exception as e:
            log_error(f"获取监控状态失败: {e}", "realtime_api.get_monitoring_status", e)
            return {
                'success': False,
                'error': str(e)
            }

# 全局服务实例
realtime_service = RealtimeMonitorService()

def get_realtime_service():
    """获取实时监控服务实例"""
    return realtime_service
