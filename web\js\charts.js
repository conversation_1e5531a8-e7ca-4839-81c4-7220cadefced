/**
 * 农业股票监控平台 - 图表渲染模块
 * 负责ECharts图表的创建和更新
 */

class ChartManager {
    constructor() {
        this.charts = {};
        this.chartConfigs = this.getChartConfigs();
    }

    /**
     * 获取图表配置
     * @returns {object} - 图表配置对象
     */
    getChartConfigs() {
        return {
            // 折线图通用配置
            lineChart: {
                backgroundColor: 'transparent',
                grid: {
                    left: '15%',
                    right: '10%',
                    top: '15%',
                    bottom: '20%'
                },
                xAxis: {
                    type: 'category',
                    axisLine: {
                        lineStyle: { color: '#4ade80' }
                    },
                    axisLabel: {
                        color: '#e8f5e8',
                        fontSize: 6, /* 缩小一倍：11 → 6 */
                        formatter: function(value) {
                            // 处理日期格式
                            if (value.includes('-')) {
                                const parts = value.split('-');
                                if (parts.length === 3) {
                                    // 日期格式：只显示月-日
                                    return parts[1] + '-' + parts[2];
                                }
                            }
                            // 处理时间格式
                            if (value.includes(' ')) {
                                const parts = value.split(' ');
                                if (parts.length === 2) {
                                    const datePart = parts[0].split('-');
                                    const timePart = parts[1].split(':')[0]; // 只取小时
                                    // 显示月-日 时
                                    return datePart[1] + '-' + datePart[2] + ' ' + timePart + 'h';
                                }
                            }
                            return value;
                        },
                        rotate: 45,
                        margin: 10
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: { color: '#4ade80' }
                    },
                    axisLabel: {
                        color: '#e8f5e8',
                        fontSize: 11,
                        formatter: function(value) {
                            if (value >= 100000000) {
                                return (value / 100000000).toFixed(1) + '亿';
                            } else if (value >= 10000000) {
                                return (value / 10000000).toFixed(0) + '千万';
                            } else if (value >= 10000) {
                                return (value / 10000).toFixed(0) + '万';
                            } else if (value >= 1000) {
                                return (value / 1000).toFixed(1) + 'K';
                            }
                            return value.toString();
                        },
                        margin: 8
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(74, 222, 128, 0.2)'
                        }
                    }
                },
                series: [{
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        color: '#22c55e',
                        width: 3
                    },
                    itemStyle: {
                        color: '#22c55e'
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: 'rgba(34, 197, 94, 0.3)' },
                                { offset: 1, color: 'rgba(34, 197, 94, 0.05)' }
                            ]
                        }
                    }
                }],
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(0, 40, 0, 0.9)',
                    borderColor: '#4ade80',
                    textStyle: {
                        color: '#e8f5e8'
                    },
                    formatter: function(params) {
                        if (params && params.length > 0) {
                            const param = params[0];
                            let value = param.value;
                            let formattedValue = value;

                            // 格式化数值显示
                            if (value >= 100000000) {
                                formattedValue = (value / 100000000).toFixed(2) + '亿';
                            } else if (value >= 10000000) {
                                formattedValue = (value / 10000000).toFixed(1) + '千万';
                            } else if (value >= 10000) {
                                formattedValue = (value / 10000).toFixed(1) + '万';
                            } else if (value >= 1000) {
                                formattedValue = (value / 1000).toFixed(1) + 'K';
                            }

                            // 格式化日期显示
                            let formattedDate = param.name;
                            if (formattedDate.includes('-')) {
                                const parts = formattedDate.split('-');
                                if (parts.length === 3) {
                                    formattedDate = parts[0] + '年' + parts[1] + '月' + parts[2] + '日';
                                }
                            }
                            if (formattedDate.includes(' ')) {
                                const parts = formattedDate.split(' ');
                                if (parts.length === 2) {
                                    const dateParts = parts[0].split('-');
                                    const timePart = parts[1].split(':')[0];
                                    formattedDate = dateParts[1] + '月' + dateParts[2] + '日 ' + timePart + '时';
                                }
                            }

                            return `${formattedDate}<br/>${param.seriesName}: ${formattedValue}`;
                        }
                        return '';
                    }
                }
            },
            // 扇形图配置
            pieChart: {
                backgroundColor: 'transparent',
                tooltip: {
                    trigger: 'item',
                    backgroundColor: 'rgba(0, 40, 0, 0.9)',
                    borderColor: '#4ade80',
                    textStyle: {
                        color: '#e8f5e8'
                    },
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    top: 'middle',
                    textStyle: {
                        color: '#e8f5e8',
                        fontSize: 12
                    },
                    formatter: function(name) {
                        if (name.length > 15) {
                            return name.substring(0, 15) + '...';
                        }
                        return name;
                    }
                },
                series: [{
                    name: '总市值',
                    type: 'pie',
                    radius: ['30%', '70%'],
                    center: ['60%', '50%'],
                    avoidLabelOverlap: false,
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '16',
                            fontWeight: 'bold',
                            color: '#4ade80'
                        }
                    },
                    labelLine: {
                        show: false
                    }
                }]
            }
        };
    }

    /**
     * 初始化图表
     * @param {string} containerId - 容器ID
     * @param {string} chartType - 图表类型
     * @returns {object} - ECharts实例
     */
    initChart(containerId, chartType) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`容器 ${containerId} 不存在`);
            return null;
        }

        // 如果图表已存在，先销毁
        if (this.charts[containerId]) {
            this.charts[containerId].dispose();
        }

        // 创建新图表
        const chart = echarts.init(container);
        this.charts[containerId] = chart;

        // 设置基础配置
        const config = JSON.parse(JSON.stringify(this.chartConfigs[chartType]));
        chart.setOption(config);

        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            chart.resize();
        });

        return chart;
    }

    /**
     * 更新折线图数据
     * @param {string} containerId - 容器ID
     * @param {object} data - 数据对象
     */
    updateLineChart(containerId, data) {
        const chart = this.charts[containerId];
        if (!chart || !data) return;

        // 格式化X轴数据
        const formattedXAxisData = (data.dates || data.hours).map(value => {
            // 处理日期格式：2024-06-21 → 06-21
            if (value.includes('-') && !value.includes(' ')) {
                const parts = value.split('-');
                if (parts.length === 3) {
                    return parts[1] + '-' + parts[2];
                }
            }
            // 处理时间格式：2024-06-21 14:00:00 → 06-21 14h
            if (value.includes(' ')) {
                const parts = value.split(' ');
                if (parts.length === 2) {
                    const datePart = parts[0].split('-');
                    const timePart = parts[1].split(':')[0];
                    return datePart[1] + '-' + datePart[2] + ' ' + timePart + 'h';
                }
            }
            return value;
        });

        const option = {
            xAxis: {
                data: formattedXAxisData,
                axisLabel: {
                    color: '#e8f5e8',
                    fontSize: 11,
                    rotate: 45,
                    margin: 10
                }
            },
            yAxis: {
                axisLabel: {
                    color: '#e8f5e8',
                    fontSize: 11,
                    formatter: function(value) {
                        if (value >= 100000000) {
                            return (value / 100000000).toFixed(1) + '亿';
                        } else if (value >= 10000000) {
                            return (value / 10000000).toFixed(0) + '千万';
                        } else if (value >= 10000) {
                            return (value / 10000).toFixed(0) + '万';
                        } else if (value >= 1000) {
                            return (value / 1000).toFixed(1) + 'K';
                        }
                        return value.toString();
                    },
                    margin: 8
                }
            },
            series: [{
                data: data.values,
                name: data.data_type === 'volume' ? '成交量' : '成交额'
            }]
        };

        chart.setOption(option);
    }

    /**
     * 更新扇形图数据
     * @param {string} containerId - 容器ID
     * @param {array} data - 数据数组
     */
    updatePieChart(containerId, data) {
        const chart = this.charts[containerId];
        if (!chart || !data) return;

        // 生成颜色数组
        const colors = this.generateColors(data.length);

        const option = {
            series: [{
                data: data.map((item, index) => ({
                    ...item,
                    itemStyle: {
                        color: colors[index]
                    }
                }))
            }],
            color: colors
        };

        chart.setOption(option);
    }

    /**
     * 生成渐变色数组
     * @param {number} count - 颜色数量
     * @returns {array} - 颜色数组
     */
    generateColors(count) {
        const colors = [];
        const baseColors = [
            '#22c55e', '#16a34a', '#15803d', '#166534', '#14532d',
            '#4ade80', '#65a30d', '#84cc16', '#a3e635', '#bef264'
        ];

        for (let i = 0; i < count; i++) {
            if (i < baseColors.length) {
                colors.push(baseColors[i]);
            } else {
                // 生成渐变色
                const hue = (120 + (i - baseColors.length) * 30) % 360;
                colors.push(`hsl(${hue}, 70%, 50%)`);
            }
        }

        return colors;
    }

    /**
     * 显示加载状态
     * @param {string} containerId - 容器ID
     */
    showLoading(containerId) {
        const chart = this.charts[containerId];
        if (chart) {
            chart.showLoading({
                text: '数据加载中...',
                color: '#4ade80',
                textColor: '#e8f5e8',
                maskColor: 'rgba(0, 40, 0, 0.8)'
            });
        }
    }

    /**
     * 隐藏加载状态
     * @param {string} containerId - 容器ID
     */
    hideLoading(containerId) {
        const chart = this.charts[containerId];
        if (chart) {
            chart.hideLoading();
        }
    }

    /**
     * 销毁图表
     * @param {string} containerId - 容器ID
     */
    destroyChart(containerId) {
        if (this.charts[containerId]) {
            this.charts[containerId].dispose();
            delete this.charts[containerId];
        }
    }

    /**
     * 销毁所有图表
     */
    destroyAllCharts() {
        Object.keys(this.charts).forEach(containerId => {
            this.destroyChart(containerId);
        });
    }
}

// 创建全局图表管理器实例
window.chartManager = new ChartManager();
