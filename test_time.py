#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
import pytz
import pymysql
from config import DatabaseConfig

def test_time_handling():
    """测试时间处理"""
    print("=== 时间处理测试 ===")
    
    # 1. 系统时间
    system_time = datetime.now()
    print(f"系统时间: {system_time}")
    
    # 2. 北京时间
    beijing_tz = pytz.timezone('Asia/Shanghai')
    beijing_time = datetime.now(beijing_tz)
    print(f"北京时间: {beijing_time}")
    
    # 3. 格式化后的时间字符串
    time_str = beijing_time.strftime('%Y-%m-%d %H:%M:%S')
    print(f"格式化时间字符串: {time_str}")
    
    # 4. 重新解析时间字符串
    parsed_time = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
    print(f"解析后的时间: {parsed_time}")
    
    # 5. 数据库时间
    try:
        db_config = DatabaseConfig()
        conn = pymysql.connect(**db_config.MYSQL_CONFIG)
        cursor = conn.cursor()
        cursor.execute('SELECT NOW();')
        db_time = cursor.fetchone()[0]
        print(f"数据库时间: {db_time}")
        conn.close()
    except Exception as e:
        print(f"数据库连接失败: {e}")
    
    # 6. JavaScript时间解析模拟
    print("\n=== JavaScript时间解析模拟 ===")
    
    # 模拟前端接收到的时间字符串
    frontend_time_str = time_str
    print(f"前端接收时间字符串: {frontend_time_str}")
    
    # 模拟添加时区信息
    beijing_time_str = frontend_time_str + '+08:00'
    print(f"添加时区信息后: {beijing_time_str}")
    
    # 模拟解析
    try:
        from dateutil import parser
        parsed_beijing_time = parser.parse(beijing_time_str)
        print(f"解析后的北京时间: {parsed_beijing_time}")
    except ImportError:
        print("需要安装 python-dateutil: pip install python-dateutil")
    except Exception as e:
        print(f"时间解析失败: {e}")

if __name__ == '__main__':
    test_time_handling()
