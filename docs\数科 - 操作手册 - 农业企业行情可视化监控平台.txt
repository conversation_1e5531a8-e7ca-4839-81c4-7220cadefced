一、项目需求分析
（一）项目背景
 在农业数字化与资本市场深度融合的背景下，农业类上市公司的行情数据呈现爆发式增长，传统的
数据处理与展示方式已无法满足实时监控与决策支持的需求。本项目聚焦农业领域上市公司，通过构建
行情可视化监控平台，实现对东方财富等公开渠道数据的高效采集、智能处理与直观展示，旨在为农业
企业战略规划、投资者决策分析及行业研究提供一站式数据解决方案，推动农业领域大数据应用的实践
落地。
（二）核心需求
1. 数据全生命周期管理
采集：支持从东方财富等网站动态爬取农业类上市公司的历史行情（如开盘价、收盘价、成交量）
及实时数据，兼容 API 接口数据接入。
处理：通过数据清洗剔除缺失值、异常值（如收盘价≤0），运用统计分析（如每日平均收盘价、总
成交量）和 LSTM 模型实现价格趋势预测。
存储：将处理后的数据持久化存储于 MySQL 数据库，支持多维度查询（如按日期、公司名称检
索）与数据备份。
1. 可视化与实时交互
基于 Flask 搭建 Web 应用，通过 Echarts 实现数据可视化，需展示当日涨幅前 10 公司柱状图、总
市值前 10 公司横向柱状图等核心指标。
集成 Spring Boot 与 Kafka，通过 WebSocket 实现实时数据推送，确保大屏展示延迟≤5 秒。
（三）项目目标
1. 实现农业类上市公司行情数据的自动化采集，准确率≥95%，每日采集频率≥1 次。
2. 构建分布式数据处理管道，数据清洗耗时≤30 分钟 / 10 万条，LSTM 预测准确率≥80%。
3. 搭建高可用数据库系统，数据写入延迟≤10 秒，查询响应时间≤2 秒。
4. 开发交互式可视化界面，页面加载时间≤3 秒，支持 100 + 并发用户访问。
5. 实现实时数据推送，故障恢复时间≤15 分钟，系统可用性≥99%。
三、项目详细设计
（一）系统架构设计
农业企业行情可视化监控平台技术架构中数据采集层通过 Python 爬虫及数据预处理从东方财富等
网站采集农业类上市公司行情数据并保存为 CSV，数据处理层利用 Spark 清洗数据、通过 Spark SQL 分
析及 LSTM 预测生成结构化结果，数据存储层将处理后的数据存入 MySQL 数据库，数据可视化层经
Flask 后端接口从数据库查询数据并通过 Echarts 前端展示为可视化图表，实时推送层由 Kafka 生产者
将实时数据发送至 Kafka 消息队列、Spring Boot 消费者监听队列并通过 WebSocket 将数据推送至数据
可视化层实现前端实时更新。
（二）模块功能设计
1. 数据采集层
核心功能：定时从目标网站采集数据，支持重试机制（默认 3 次），规避反爬策略（如设置 User -
Agent、请求间隔 5 秒）。
输入：东方财富农业股行情 URL（如 https://eastmoney.com/agricultural_stocks ）。
输出：结构化 CSV 文件（字段包括公司名称、日期、开盘价、收盘价等）。
2. 数据处理层
数据清洗
缺失值处理：使用 dropna() 剔除包含 NULL 的行。
异常值处理：过滤收盘价≤0 的数据，保留有效交易记录。
数据分析
每日统计：按日期分组计算平均收盘价、总成交量。
排名查询：通过 Spark SQL 获取当日涨幅 / 总市值前 10 公司。
数据预测
模型输入：历史收盘价序列（时间步长 100）。
模型结构：3 层 LSTM 网络（每层 50 单元），Dense 输出层。
训练配置：Adam 优化器，均方误差损失，训练 100 epoch，批次大小 64。
3. 数据存储层
Hive数据库设计
-- 1. 上市公司信息表
CREATE EXTERNAL TABLE IF NOT EXISTS stock_company_info (
 stock_code STRING COMMENT '股票代码',
 company_name STRING COMMENT '公司名称',
 industry STRING COMMENT '所属行业',
 sub_industry STRING COMMENT '细分行业',
 listing_date DATE COMMENT '上市日期',
 registered_address STRING COMMENT '注册地址',
 actual_controller STRING COMMENT '实际控制人'
)
PARTITIONED BY (dt STRING)
STORED AS PARQUET
LOCATION '/data/agricultural/stock/company_info';
-- 2. 股票日行情表
CREATE EXTERNAL TABLE IF NOT EXISTS stock_daily_quote (
 stock_code STRING COMMENT '股票代码',
 trade_date DATE COMMENT '交易日期',
 opening_price DECIMAL(10,2) COMMENT '开盘价',
 closing_price DECIMAL(10,2) COMMENT '收盘价',
 highest_price DECIMAL(10,2) COMMENT '最高价',
 lowest_price DECIMAL(10,2) COMMENT '最低价',
 trading_volume BIGINT COMMENT '成交量(股)',
 trading_amount DECIMAL(10,2) COMMENT '成交额(元)',
 change_percent DECIMAL(10,4) COMMENT '涨跌幅',
 turnover_rate DECIMAL(10,4) COMMENT '换手率',
 pe_ratio DECIMAL(10,2) COMMENT '市盈率'
)
PARTITIONED BY (dt STRING)
STORED AS PARQUET
LOCATION '/data/agricultural/stock/daily_quote';
-- 3. 财务指标表
CREATE EXTERNAL TABLE IF NOT EXISTS financial_indicators (
 stock_code STRING COMMENT '股票代码',
 report_period STRING COMMENT '报告期(季度/年度)',
 operating_revenue DECIMAL(10,2) COMMENT '营业收入(万元)',
 net_profit DECIMAL(10,2) COMMENT '净利润(万元)',
 eps DECIMAL(10,4) COMMENT '每股收益(元)',
 bps DECIMAL(10,4) COMMENT '每股净资产(元)',
 roe DECIMAL(10,4) COMMENT '净资产收益率',
 debt_ratio DECIMAL(10,4) COMMENT '资产负债率'
)
PARTITIONED BY (dt STRING)
STORED AS PARQUET
LOCATION '/data/agricultural/stock/financial_indicators';
-- 4. 行业指数表
CREATE EXTERNAL TABLE IF NOT EXISTS industry_index (
 industry_code STRING COMMENT '行业代码',
 industry_name STRING COMMENT '行业名称',
 trade_date DATE COMMENT '交易日期',
 closing_index DECIMAL(10,2) COMMENT '收盘指数',
 change_percent DECIMAL(10,4) COMMENT '涨跌幅',
 trading_volume BIGINT COMMENT '成交量(手)',
 trading_amount DECIMAL(10,2) COMMENT '成交额(万元)'
)
PARTITIONED BY (dt STRING)
STORED AS PARQUET
LOCATION '/data/agricultural/stock/industry_index';
-- 5. 股东结构表
CREATE EXTERNAL TABLE IF NOT EXISTS shareholder_structure (
 stock_code STRING COMMENT '股票代码',
 report_date DATE COMMENT '报告日期',
 shareholder_rank INT COMMENT '股东排名',
 shareholder_name STRING COMMENT '股东名称',
 shareholding_amount BIGINT COMMENT '持股数量(股)',
 shareholding_ratio DECIMAL(10,4) COMMENT '持股比例',
 shareholding_nature STRING COMMENT '股东性质'
)
PARTITIONED BY (dt STRING)
STORED AS PARQUET
LOCATION '/data/agricultural/stock/shareholder_structure';
4. 数据可视化层
前端页面
路由： / ，展示当日涨幅和总市值前 10 公司图表。
技术实现：
Flask 后端通过 SQLAlchemy 查询数据库，返回 JSON 数据。
Echarts 渲染柱状图，X 轴为公司名称，Y 轴为数值（涨幅 / 总市值）。
5. 实时推送层
流程
1. 数据采集后，通过 Kafka 生产者发送至主题 agricultural_stocks_topic 。
from kafka import KafkaProducer
import json
import time
import random
from datetime import datetime
KAFKA_BOOTSTRAP_SERVERS = ['localhost:9092']
KAFKA_TOPIC = 'agricultural_stock_topic'
STOCK_CODES = ['000860.SZ', '002041.SZ', '002299.SZ', '600598.SH', 
 '600354.SH', '000998.SZ', '002556.SZ', '600108.SH']
producer = KafkaProducer(
 bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
 value_serializer=lambda v: json.dumps(v).encode('utf-8'), # 序列化消息为JSON
 key_serializer=lambda k: str(k).encode('utf-8'), # 序列化键
 retries=3 # 重试次数
)
def generate_stock_data(stock_code):
 """生成模拟的股票行情数据"""
 now = datetime.now()
 base_price = random.uniform(5, 50)
 return {
 "stock_code": stock_code,
 "timestamp": now.strftime("%Y-%m-%d %H:%M:%S"),
 "price": round(base_price, 2),
 "change": round(random.uniform(-2, 2), 2),
 "volume": random.randint(10000, 1000000),
 "amount": round(base_price * random.randint(10000, 1000000), 2),
 "market_cap": round(random.uniform(1000, 100000), 2)
 }
try:
 print(f"开始向Kafka主题 {KAFKA_TOPIC} 发送数据...")
 
except Exception as e:
 print(f"发生错误: {e}")
finally:
 producer.flush()
 producer.close()
 print("Kafka生产者已关闭")
2. Spring Boot 消费者监听主题，接收数据后通过 WebSocket 推送给前端。
while True:
# 为每支股票生成一条数据
for stock_code in STOCK_CODES:
stock_data = generate_stock_data(stock_code)
# 发送消息到Kafka
future = producer.send(
topic=KAFKA_TOPIC,
key=stock_code, # 使用股票代码作为键
value=stock_data
)
# 阻塞等待确认
record_metadata = future.get(timeout=10)
print(f"已发送到分区: {record_metadata.partition}, 偏移量:
{record_metadata.offset}")
# 每5秒生成一批数据
time.sleep(5)
3. 前端通过长连接实时更新大屏数据。
四、功能模块开发
（一）数据采集模块
python 运行
（二）数据处理模块
Python导入Pyspark的包，具体代码如下：
# 完整代码示例（含反爬优化）
import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
from fake_useragent import UserAgent # 引入随机UA库
def get_stock_data(url, retry=3, delay=5):
ua = UserAgent()
headers = {"User - Agent": ua.random} # 随机生成User - Agent
for _ in range(retry):
try:
response = requests.get(url, headers=headers, timeout=10)
response.raise_for_status()
soup = BeautifulSoup(response.text, "lxml") # 使用lxml解析器
table = soup.find("table", class_="qox_table") # 定位具体表格
if not table:
raise ValueError("未找到目标表格")
df = pd.read_html(str(table))[0]
# 数据预处理：删除无关行（如总计行）
df = df[df["公司名称"] != "合计"]
return df
except Exception as e:
print(f"第{retry - _}次重试失败: {e}")
time.sleep(delay)
return None
# 调用示例（需替换为真实URL）
url = "https://eastmoney.com/concept/agricultural_stocks.html"
data = get_stock_data(url)
if data is not None:
data.to_csv("agricultural_stocks.csv", index=False, encoding="utf - 8")
from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.window import Window
# 创建SparkSession
spark = SparkSession.builder \
.appName("AgriculturalStockAnalysis") \
.config("spark.sql.shuffle.partitions", "20") \
.getOrCreate()
# 读取股票日行情数据
quote_df = spark.read.parquet("/data/agricultural/stock/daily_quote/*")
# 读取公司信息数据
company_df = spark.read.parquet("/data/agricultural/stock/company_info/*")
# 数据清洗 - 过滤无效数据
valid_quote_df = quote_df.filter(
(col("closing_price") > 0) &
(col("trading_volume") > 0)
)
# 关联公司信息和股票行情
joined_df = valid_quote_df.join(
company_df,
on="stock_code",
how="inner"
)
# 计算行业每日平均涨跌幅
industry_daily_change = joined_df.groupBy(
"industry", "trade_date"
).agg(
avg("change_percent").alias("avg_change_percent"),
sum("trading_amount").alias("total_trading_amount")
)
# 窗口函数 - 计算连续上涨天数
window_spec = Window.partitionBy("stock_code").orderBy("trade_date")
price_change_df = joined_df.withColumn(
"prev_close", lag("closing_price", 1).over(window_spec)
).withColumn(
"is_up", when(col("closing_price") > col("prev_close"), 1).otherwise(0)
)
# 计算连续上涨最大天数
consecutive_up_days = price_change_df.withColumn(
"grp", col("trade_date").cast("long") - row_number().over(window_spec)
).groupBy("stock_code", "grp").agg(
max("is_up").alias("max_consecutive_up")
).groupBy("stock_code").agg(
max("max_consecutive_up").alias("max_up_days")
)
# 计算每个行业表现最好的3只股票
top_stocks = joined_df.groupBy("industry", "stock_code").agg(
avg("change_percent").alias("avg_change"),
sum("trading_volume").alias("total_volume")
)
window_spec_top = Window.partitionBy("industry").orderBy(desc("avg_change"))
top3_stocks = top_stocks.withColumn(
"rank", rank().over(window_spec_top)
).filter(col("rank") <= 3)
（三）数据可视化模块
python 运行
# 保存分析结果
industry_daily_change.write.parquet(
"/output/agricultural/industry_daily_change",
mode="overwrite"
)
consecutive_up_days.write.parquet(
"/output/agricultural/consecutive_up_days",
mode="overwrite"
)
top3_stocks.write.parquet(
"/output/agricultural/top3_stocks",
mode="overwrite"
)
# 停止SparkSession
spark.stop()
# Flask后端代码（含数据库连接池优化）
from flask import Flask, render_template, jsonify
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import text
app = Flask(__name__)
app.config.update(
SQLALCHEMY_DATABASE_URI="mysql+pymysql://root:password@localhost:3306/agricultu
ral_data?charset=utf8mb4",
SQLALCHEMY_POOL_SIZE=10, # 连接池大小
SQLALCHEMY_POOL_RECYCLE=3600,
SQLALCHEMY_TRACK_MODIFICATIONS=False
)
db = SQLAlchemy(app)
@app.route("/api/top_growth")
def get_top_growth():
query = text("""
SELECT 公司名称,
FORMAT((收盘价 - 开盘价) / 开盘价 * 100, 2) AS 涨幅
FROM stock_data
WHERE 日期 = CURDATE()
ORDER BY 涨幅 DESC
LIMIT 10
""")
result = db.session.execute(query).fetchall()
return jsonify([dict(row) for row in result]) # 返回JSON格式数据
@app.route("/")
def index():
html 预览
return render_template("index.html")
if __name__ == "__main__":
app.run(host="0.0.0.0", port=5000, debug=False)
<!-- Echarts前端代码（异步加载数据） -->
<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>农业股行情监控</title>
<script
src="https://cdn.jsdelivr.net/npm/echarts@5.4.2/dist/echarts.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/axios@0.21.1/dist/axios.min.js">
</script>
</head>
<body>
<div id="growthChart" style="width: 1200px; height: 500px;"></div>
<script>
const chart = echarts.init(document.getElementById("growthChart"));
let xData = [], yData = [];
// 异步获取数据
axios.get("/api/top_growth")
.then(res => {
res.data.forEach(item => {
xData.push(item.公司名称);
yData.push(parseFloat(item.涨幅));
});
chart.setOption({
xAxis: { type: 'category', data: xData, axisLabel: { rotate:
45 } },
yAxis: { type: 'value', name: '涨幅(%)' },
series: [{
type: 'bar',
data: yData,
itemStyle: { color: '#4ECDC4' }
}]
});
})
.catch(err => console.error("数据加载失败:", err));
</script>
</body>
</html>