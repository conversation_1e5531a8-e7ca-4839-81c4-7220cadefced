/**
 * 农业股票监控平台 - 排行榜页面逻辑
 * 负责排行榜数据加载和图表渲染
 */

class RankingPage {
    constructor() {
        this.charts = {};
        this.refreshInterval = 30000; // 30秒刷新间隔
        this.timers = {};
    }

    /**
     * 初始化排行榜页面
     */
    async init() {
        try {
            console.log('初始化排行榜页面...');
            
            // 初始化图表
            this.initCharts();
            
            // 加载初始数据
            await this.loadAllData();
            
            // 设置事件监听
            this.setupEventListeners();
            
            // 设置自动刷新
            this.setupAutoRefresh();
            
            console.log('排行榜页面初始化完成');
            
        } catch (error) {
            console.error('排行榜页面初始化失败:', error);
        }
    }

    /**
     * 初始化图表
     */
    initCharts() {
        // 初始化涨幅排行图表
        this.charts.gainers = echarts.init(document.getElementById('gainers-chart'));
        
        // 初始化市值排行图表
        this.charts.marketCap = echarts.init(document.getElementById('market-cap-chart'));
        
        console.log('排行榜图表初始化完成');
    }

    /**
     * 加载所有数据
     */
    async loadAllData() {
        const loadPromises = [
            this.loadGainersData(),
            this.loadMarketCapData()
        ];

        await Promise.allSettled(loadPromises);
    }

    /**
     * 加载涨幅排行数据
     */
    async loadGainersData() {
        try {
            this.showLoading('gainers-chart');
            const data = await dashboardAPI.getTopGainersData();

            if (data && Array.isArray(data)) {
                console.log('涨幅排行数据:', data);
                this.updateGainersChart(data);
            } else {
                console.error('涨幅排行数据格式错误:', data);
            }
        } catch (error) {
            console.error('加载涨幅排行数据失败:', error);
        } finally {
            this.hideLoading('gainers-chart');
        }
    }

    /**
     * 加载市值排行数据
     */
    async loadMarketCapData() {
        try {
            this.showLoading('market-cap-chart');
            const data = await dashboardAPI.getTopMarketCapData();

            if (data && Array.isArray(data)) {
                console.log('市值排行数据:', data);
                this.updateMarketCapChart(data);
            } else {
                console.error('市值排行数据格式错误:', data);
            }
        } catch (error) {
            console.error('加载市值排行数据失败:', error);
        } finally {
            this.hideLoading('market-cap-chart');
        }
    }

    /**
     * 更新涨幅排行图表
     */
    updateGainersChart(data) {
        const option = {
            backgroundColor: 'transparent',
            grid: {
                left: '25%',
                right: '15%',
                top: '5%',
                bottom: '5%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                axisLine: { lineStyle: { color: '#4ade80' } },
                axisLabel: {
                    color: '#e8f5e8',
                    fontSize: 14,
                    formatter: function(value) {
                        return value + '%';
                    }
                },
                splitLine: {
                    lineStyle: { color: 'rgba(74, 222, 128, 0.2)' }
                }
            },
            yAxis: {
                type: 'category',
                data: data.map(item => item.stock_name),
                axisLine: { lineStyle: { color: '#4ade80' } },
                axisLabel: {
                    color: '#e8f5e8',
                    fontSize: 14
                },
                inverse: true // 倒序显示，最高的在上面
            },
            series: [{
                type: 'bar',
                data: data.map(item => ({
                    value: item.value,
                    itemStyle: {
                        color: item.value >= 0 ? '#22c55e' : '#ef4444'
                    }
                })),
                barWidth: '70%',
                label: {
                    show: true,
                    position: 'right',
                    color: '#e8f5e8',
                    fontSize: 14,
                    formatter: function(params) {
                        return params.value + '%';
                    }
                }
            }],
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(0, 40, 0, 0.9)',
                borderColor: '#4ade80',
                textStyle: { color: '#e8f5e8' },
                formatter: function(params) {
                    if (params && params.length > 0) {
                        const param = params[0];
                        const color = param.value >= 0 ? '[UP]' : '[DOWN]';
                        return `${color} ${param.name}<br/>涨跌幅: ${param.value}%`;
                    }
                    return '';
                }
            }
        };

        this.charts.gainers.setOption(option);
    }

    /**
     * 更新市值排行图表
     */
    updateMarketCapChart(data) {
        const option = {
            backgroundColor: 'transparent',
            grid: {
                left: '25%',
                right: '20%',
                top: '5%',
                bottom: '5%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                axisLine: { lineStyle: { color: '#4ade80' } },
                axisLabel: {
                    color: '#e8f5e8',
                    fontSize: 14,
                    formatter: function(value) {
                        if (value >= 100000000000) {
                            return (value / 100000000000).toFixed(1) + '千亿';
                        } else if (value >= 10000000000) {
                            return (value / 10000000000).toFixed(0) + '百亿';
                        } else if (value >= 1000000000) {
                            return (value / 1000000000).toFixed(1) + '十亿';
                        }
                        return value;
                    }
                },
                splitLine: {
                    lineStyle: { color: 'rgba(74, 222, 128, 0.2)' }
                }
            },
            yAxis: {
                type: 'category',
                data: data.map(item => item.stock_name),
                axisLine: { lineStyle: { color: '#4ade80' } },
                axisLabel: {
                    color: '#e8f5e8',
                    fontSize: 14
                },
                inverse: true // 倒序显示，最高的在上面
            },
            series: [{
                type: 'bar',
                data: data.map(item => item.value),
                barWidth: '70%',
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                        { offset: 0, color: '#4ade80' },
                        { offset: 1, color: '#22c55e' }
                    ])
                },
                label: {
                    show: true,
                    position: 'right',
                    color: '#e8f5e8',
                    fontSize: 14,
                    formatter: function(params) {
                        const value = params.value;
                        if (value >= 100000000000) {
                            return (value / 100000000000).toFixed(1) + '千亿';
                        } else if (value >= 10000000000) {
                            return (value / 10000000000).toFixed(0) + '百亿';
                        } else if (value >= 1000000000) {
                            return (value / 1000000000).toFixed(1) + '十亿';
                        }
                        return value;
                    }
                }
            }],
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(0, 40, 0, 0.9)',
                borderColor: '#4ade80',
                textStyle: { color: '#e8f5e8' },
                formatter: function(params) {
                    if (params && params.length > 0) {
                        const param = params[0];
                        const value = param.value;
                        let formattedValue;
                        if (value >= 100000000000) {
                            formattedValue = (value / 100000000000).toFixed(2) + '千亿';
                        } else if (value >= 10000000000) {
                            formattedValue = (value / 10000000000).toFixed(1) + '百亿';
                        } else if (value >= 1000000000) {
                            formattedValue = (value / 1000000000).toFixed(2) + '十亿';
                        } else {
                            formattedValue = value;
                        }
                        return `[VALUE] ${param.name}<br/>总市值: ${formattedValue}`;
                    }
                    return '';
                }
            }
        };

        this.charts.marketCap.setOption(option);
    }

    /**
     * 显示加载状态
     */
    showLoading(chartId) {
        const chart = this.charts[chartId === 'gainers-chart' ? 'gainers' : 'marketCap'];
        if (chart) {
            chart.showLoading({
                text: '加载中...',
                color: '#4ade80',
                textColor: '#e8f5e8',
                maskColor: 'rgba(0, 0, 0, 0.3)'
            });
        }
    }

    /**
     * 隐藏加载状态
     */
    hideLoading(chartId) {
        const chart = this.charts[chartId === 'gainers-chart' ? 'gainers' : 'marketCap'];
        if (chart) {
            chart.hideLoading();
        }
    }

    /**
     * 设置事件监听
     */
    setupEventListeners() {
        // 窗口大小变化时重新调整图表和布局
        window.addEventListener('resize', () => {
            this.handleWindowResize();
        });

        // 初始调整
        this.handleWindowResize();
    }

    /**
     * 处理窗口大小变化
     */
    handleWindowResize() {
        // 调整图表大小
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.resize();
            }
        });

        // 动态调整窗口布局
        this.adjustWindowLayout();
    }

    /**
     * 动态调整窗口布局
     */
    adjustWindowLayout() {
        // CSS媒体查询已经处理了布局，这里只需要确保图表正确渲染
        // 移除动态样式设置，避免与CSS冲突
        console.log('窗口布局调整完成，依赖CSS媒体查询');
    }

    /**
     * 设置自动刷新
     */
    setupAutoRefresh() {
        this.timers.autoRefresh = setInterval(() => {
            this.loadAllData();
        }, this.refreshInterval);

        console.log('排行榜自动刷新已设置');
    }



    /**
     * 清除定时器
     */
    clearTimers() {
        Object.values(this.timers).forEach(timer => {
            if (timer) clearInterval(timer);
        });
        this.timers = {};
    }

    /**
     * 销毁页面
     */
    destroy() {
        this.clearTimers();
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.dispose();
        });
        this.charts = {};
        console.log('排行榜页面已销毁');
    }
}

// 创建全局排行榜实例
window.rankingPage = new RankingPage();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    rankingPage.init();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    rankingPage.destroy();
});
