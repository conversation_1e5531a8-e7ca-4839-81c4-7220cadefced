<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>农业股票监控平台 - 实时监测</title>
    <link rel="stylesheet" href="../css/main.css">
    <style>
        body {
            background-image: url('../image/4.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            background-repeat: no-repeat;
        }
        body::before {
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(96, 165, 250, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(147, 197, 253, 0.1) 0%, transparent 50%);
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- 顶部导航栏 -->
        <header class="header">
            <div class="logo">
                <div class="logo-icon">🌾</div>
                <div class="logo-text">农业股票监控平台</div>
            </div>
            <nav class="nav-buttons">
                <a href="main_dashboard.html" class="nav-btn" data-page="dashboard">数据大屏</a>
                <a href="prediction.html" class="nav-btn" data-page="prediction">未来预测</a>
                <a href="ranking.html" class="nav-btn" data-page="ranking">今日排行榜</a>
                <a href="realtime_monitor.html" class="nav-btn active" data-page="realtime">实时监测</a>
            </nav>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <div class="realtime-container">
                <!-- 控制面板 -->
                <div class="control-panel">
                    <div class="control-group stock-selection">
                        <label for="stock-select">选择监控股票:</label>
                        <select id="stock-select" class="control-select">
                            <option value="">请选择股票...</option>
                        </select>
                    </div>

                    <div class="control-group">
                        <span class="status-indicator" id="status-indicator">
                            <span class="status-dot" id="status-dot"></span>
                            <span id="status-text">未监控</span>
                        </span>
                    </div>

                    <button id="start-monitor-btn" class="monitor-btn" disabled>
                        开始监控
                    </button>
                    <button id="stop-monitor-btn" class="monitor-btn stop-btn" disabled>
                        停止监控
                    </button>
                </div>

                <!-- 图表区域 -->
                <div class="charts-container">
                    <!-- 价格和涨跌幅图表 -->
                    <div class="chart-window">
                        <div class="window-header">
                            <h3>📈 价格走势</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="price-chart"></canvas>
                        </div>
                    </div>

                    <!-- 成交量和成交额图表 -->
                    <div class="chart-window">
                        <div class="window-header">
                            <h3>📊 成交数据</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="volume-chart"></canvas>
                        </div>
                    </div>

                    <!-- 估值指标图表 -->
                    <div class="chart-window">
                        <div class="window-header">
                            <h3>📋 估值指标</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="valuation-chart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 数据展示区域 -->
                <div class="data-display">
                    <div class="data-item">
                        <div class="data-label">当前价格</div>
                        <div class="data-value" id="current-price">--</div>
                        <div class="data-unit">元</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">涨跌幅</div>
                        <div class="data-value" id="change-percent">--</div>
                        <div class="data-unit">%</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">成交量</div>
                        <div class="data-value" id="volume">--</div>
                        <div class="data-unit">手</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">成交额</div>
                        <div class="data-value" id="turnover">--</div>
                        <div class="data-unit">万元</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">换手率</div>
                        <div class="data-value" id="turnover-rate">--</div>
                        <div class="data-unit">%</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">市盈率</div>
                        <div class="data-value" id="pe-ratio">--</div>
                        <div class="data-unit">倍</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">市净率</div>
                        <div class="data-value" id="pb-ratio">--</div>
                        <div class="data-unit">倍</div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="../js/realtime_monitor.js"></script>
</body>
</html>
