/**
 * 农业股票监控平台 - API调用模块
 * 负责与后端API接口的数据交互
 */

class DashboardAPI {
    constructor() {
        this.baseURL = 'http://localhost:5000/api';
        this.timeout = 10000; // 10秒超时
        this.useMockData = false; // 使用真实数据库数据
    }

    /**
     * 通用API请求方法
     * @param {string} endpoint - API端点
     * @param {object} options - 请求选项
     * @returns {Promise} - 返回Promise对象
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            ...options
        };

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout);
            
            const response = await fetch(url, {
                ...config,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'API请求失败');
            }

            return data.data;
        } catch (error) {
            console.error(`API请求失败 [${endpoint}]:`, error);
            throw error;
        }
    }

    /**
     * 生成模拟数据
     */
    generateMockData90d(dataType) {
        const stockCodes = ['000553', '600519', '002304', '000858', '600257'];
        const stockNames = ['沃华医药', '贵州茅台', '洋河股份', '五粮液', '大湖股份'];
        const randomIndex = Math.floor(Math.random() * stockCodes.length);

        const dates = [];
        const values = [];
        const baseValue = dataType === 'volume' ? 5000000 : 500000000;

        for (let i = 0; i < 60; i++) {
            const date = new Date();
            date.setDate(date.getDate() - (60 - i));
            dates.push(date.toISOString().split('T')[0]);
            values.push(Math.floor(baseValue + Math.random() * baseValue * 0.5));
        }

        return {
            stock_code: stockCodes[randomIndex],
            stock_name: stockNames[randomIndex],
            dates: dates,
            values: values,
            data_type: dataType
        };
    }

    generateMockData7d(dataType) {
        const stockCodes = ['000553', '600519', '002304', '000858', '600257'];
        const stockNames = ['沃华医药', '贵州茅台', '洋河股份', '五粮液', '大湖股份'];
        const randomIndex = Math.floor(Math.random() * stockCodes.length);

        const hours = [];
        const values = [];
        const baseValue = dataType === 'volume' ? 500000 : 50000000;

        for (let i = 0; i < 168; i++) { // 7天 * 24小时
            const date = new Date();
            date.setHours(date.getHours() - (168 - i));
            hours.push(date.toISOString().replace('T', ' ').split('.')[0]);
            values.push(Math.floor(baseValue + Math.random() * baseValue * 0.3));
        }

        return {
            stock_code: stockCodes[randomIndex],
            stock_name: stockNames[randomIndex],
            hours: hours,
            values: values,
            data_type: dataType
        };
    }

    generateMockMarketCap() {
        const stocks = [
            { code: '000553', name: '沃华医药' },
            { code: '600519', name: '贵州茅台' },
            { code: '002304', name: '洋河股份' },
            { code: '000858', name: '五粮液' },
            { code: '600257', name: '大湖股份' },
            { code: '000876', name: '新希望' },
            { code: '002714', name: '牧原股份' },
            { code: '000998', name: '隆平高科' }
        ];

        return stocks.map(stock => ({
            name: `${stock.name}(${stock.code})`,
            value: Math.floor(Math.random() * 5000000000 + 1000000000),
            stock_code: stock.code
        }));
    }

    /**
     * 获取随机股票90天成交量数据
     * @returns {Promise} - 返回90天成交量数据
     */
    async getRandomStock90dVolume() {
        if (this.useMockData) {
            return new Promise(resolve => {
                setTimeout(() => resolve(this.generateMockData90d('volume')), 500);
            });
        }
        return await this.request('/random-stock-90d/volume');
    }

    /**
     * 获取随机股票90天成交额数据
     * @returns {Promise} - 返回90天成交额数据
     */
    async getRandomStock90dTurnover() {
        if (this.useMockData) {
            return new Promise(resolve => {
                setTimeout(() => resolve(this.generateMockData90d('turnover')), 500);
            });
        }
        return await this.request('/random-stock-90d/turnover');
    }

    /**
     * 获取随机股票7天成交量数据
     * @returns {Promise} - 返回7天成交量数据
     */
    async getRandomStock7dVolume() {
        if (this.useMockData) {
            return new Promise(resolve => {
                setTimeout(() => resolve(this.generateMockData7d('volume')), 500);
            });
        }
        return await this.request('/random-stock-7d/volume');
    }

    /**
     * 获取随机股票7天成交额数据
     * @returns {Promise} - 返回7天成交额数据
     */
    async getRandomStock7dTurnover() {
        if (this.useMockData) {
            return new Promise(resolve => {
                setTimeout(() => resolve(this.generateMockData7d('turnover')), 500);
            });
        }
        return await this.request('/random-stock-7d/turnover');
    }

    /**
     * 获取总市值数据
     * @returns {Promise} - 返回总市值数据
     */
    async getMarketCapData() {
        if (this.useMockData) {
            return new Promise(resolve => {
                setTimeout(() => resolve(this.generateMockMarketCap()), 800);
            });
        }
        return await this.request('/market-cap');
    }

    /**
     * 获取当日涨幅前十数据
     * @returns {Promise} - 返回涨幅排行数据
     */
    async getTopGainersData() {
        if (this.useMockData) {
            return new Promise(resolve => {
                setTimeout(() => resolve(this.generateMockTopGainersData()), 500);
            });
        }
        return await this.request('/top-gainers');
    }

    /**
     * 获取总市值前十数据
     * @returns {Promise} - 返回市值排行数据
     */
    async getTopMarketCapData() {
        if (this.useMockData) {
            return new Promise(resolve => {
                setTimeout(() => resolve(this.generateMockTopMarketCapData()), 500);
            });
        }
        return await this.request('/top-market-cap');
    }

    /**
     * 健康检查
     * @returns {Promise} - 返回健康状态
     */
    async healthCheck() {
        return await this.request('/health');
    }

    /**
     * 生成模拟涨幅排行数据
     */
    generateMockTopGainersData() {
        const stocks = [
            { code: '000553', name: '沃华医药' },
            { code: '600519', name: '贵州茅台' },
            { code: '002304', name: '洋河股份' },
            { code: '000858', name: '五粮液' },
            { code: '600257', name: '大湖股份' },
            { code: '000876', name: '新希望' },
            { code: '002714', name: '牧原股份' },
            { code: '000998', name: '隆平高科' },
            { code: '600298', name: '安琪酵母' },
            { code: '000735', name: '罗牛山' }
        ];

        const data = stocks.map(stock => ({
            name: `${stock.name}(${stock.code})`,
            value: parseFloat((Math.random() * 15 - 5).toFixed(2)), // -5% 到 +10%
            stock_code: stock.code,
            stock_name: stock.name
        }));

        return data.sort((a, b) => b.value - a.value);
    }

    /**
     * 生成模拟市值排行数据
     */
    generateMockTopMarketCapData() {
        const stocks = [
            { code: '000553', name: '沃华医药' },
            { code: '600519', name: '贵州茅台' },
            { code: '002304', name: '洋河股份' },
            { code: '000858', name: '五粮液' },
            { code: '600257', name: '大湖股份' },
            { code: '000876', name: '新希望' },
            { code: '002714', name: '牧原股份' },
            { code: '000998', name: '隆平高科' },
            { code: '600298', name: '安琪酵母' },
            { code: '000735', name: '罗牛山' }
        ];

        const data = stocks.map(stock => ({
            name: `${stock.name}(${stock.code})`,
            value: Math.floor(Math.random() * 500000000000) + 10000000000, // 100亿到5000亿
            stock_code: stock.code,
            stock_name: stock.name
        }));

        return data.sort((a, b) => b.value - a.value);
    }
}

// 创建全局API实例
window.dashboardAPI = new DashboardAPI();
